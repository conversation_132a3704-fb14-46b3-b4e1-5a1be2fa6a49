# Simplified SSE + WebContainer Architecture

## Overview

The architecture has been simplified to remove unnecessary complexity and follow your preferences:

- **No `useDebounce`** - Removed debouncing logic as it's redundant with SSE
- **Clean SSE implementation** - No fallback timeouts, pure event-driven
- **Decoupled hooks** - Separate concerns for better maintainability
- **Minimal Zustand usage** - Only for state sync and loading indicators
- **Simple WebContainer integration** - Only runs when there's generated data

## Key Changes Made

### 1. Created `useSSE` Hook (`src/hooks/useSSE.ts`)

```typescript
// Clean SSE hook - no timeouts, no fallbacks
const { isConnected, lastEvent } = useSSE(generationId, {
  onEvent: (event) => {
    // Handle SSE events directly
    switch (event.type) {
      case 'generation_progress':
        updateStreaming(event.data.currentResult);
        break;
      case 'generation_completed':
        stopStreaming();
        if (!isDocumentationMode) {
          initializeWithGeneratedFiles(event.data.result);
        }
        break;
    }
  }
});
```

### 2. Simplified WebContainer Hook (`src/hooks/useSimpleWebContainer.ts`)

```typescript
// Only runs when there's generated data
const {
  initializeWithGeneratedFiles,
  updateFile,
  updateAllFiles,
  isOperational
} = useSimpleWebContainer(!isDocumentationMode);
```

### 3. Cleaned Up Generation Store

Removed complex sync operations:
- ❌ `syncToWebContainer`
- ❌ `syncAllToWebContainer` 
- ❌ `queueForSync`
- ❌ `toggleAutoSync`
- ❌ `needsSync`
- ❌ `canSyncToWebContainer`

Kept only essential state:
- ✅ `isDirty`
- ✅ `dirtyFiles`
- ✅ `markFileClean`

### 4. Removed Debounced Sync

- Deleted `src/hooks/useDebouncedSync.ts`
- No more complex timeout management
- No more sync queues or conflict detection

## Architecture Flow

### UI Generation Flow
1. **SSE Event** → `generation_progress` → Update chat panel with streaming content
2. **SSE Event** → `generation_completed` → Stop streaming + Initialize WebContainer
3. **WebContainer** → Bootstrap Vite + React + Tailwind + Lucide
4. **File Explorer** + **Code Editor** + **WebContainer Preview** shown
5. **Code changes** → Direct WebContainer file updates (no debouncing)

### Documentation Generation Flow
1. **SSE Event** → `generation_progress` → Update chat panel with streaming content
2. **SSE Event** → `generation_completed` → Stop streaming
3. **Chat Panel** + **Code Editor** + **React-Markdown Preview** shown
4. **No WebContainer** (disabled for documentation mode)

## Panel Configurations

### UI Generation
- **File Explorer** (left) - Shows generated file structure
- **Code Editor** (center) - Monaco editor with syntax highlighting
- **WebContainer Preview** (right) - Live preview with hot reload

### Documentation Generation  
- **Chat Panel** (left) - AI assistant conversation
- **Code Editor** (center) - Monaco editor for code examples
- **Markdown Preview** (right) - React-markdown rendered output

## State Management with Zustand

Zustand is now used minimally for:

1. **State Sync** - Keep file content synced between components
2. **Loading Indicators** - Track streaming, WebContainer boot states
3. **UI State** - Active file, panel visibility, etc.

```typescript
// Simple state updates
const { startStreaming, updateStreaming, stopStreaming } = useGenerationStore();

// No complex sync logic
startStreaming(content);  // Show loading
updateStreaming(content); // Update content
stopStreaming();          // Hide loading
```

## Benefits

1. **Simpler** - Removed 200+ lines of complex debouncing logic
2. **More Reliable** - Event-driven architecture is more predictable
3. **Better Performance** - No unnecessary timeouts or polling
4. **Easier to Debug** - Clear event flow, less state to track
5. **Maintainable** - Decoupled concerns, single responsibility

## WebContainer Lifecycle

```
SSE Event (generation_completed)
    ↓
initializeWithGeneratedFiles(result)
    ↓
createFileSystemFromCode(cleanedCode)
    ↓
mountFiles() → installDependencies() → startDevServer()
    ↓
WebContainer Ready (URL available)
```

## File Updates

```
User edits code in Monaco Editor
    ↓
handleCodeChange(fileId, content)
    ↓
updateFile(fileId, content) [300ms timeout]
    ↓
WebContainer file updated
    ↓
Hot reload in preview
```

This architecture is much cleaner and follows your preferences for simplicity over complex safety mechanisms.
