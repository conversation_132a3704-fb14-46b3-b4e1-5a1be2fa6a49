import { CommonFunction } from '@/common/Function/Function';
import { Api } from '@/common/StandardApi';
import { ApiURL } from '@/config/config';

export interface SavePromptDto {
	prompt: string;
	type: 'DOCUMENTATION' | 'UI';
	description?: string;
	tags?: string[];
	projectId?: string;
	createdById: string;
}

class PromptAPI {
	_api: Api;

	constructor() {
		this._api = new Api(ApiURL);
	}

	async savePrompt(body: SavePromptDto) {
		return await this._api.post(
			`prompt/save`,
			JSON.stringify(body),
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}
}

export { PromptAPI };
