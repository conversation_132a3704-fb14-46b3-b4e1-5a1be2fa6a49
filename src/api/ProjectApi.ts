import { CommonFunction } from '@/common/Function/Function';
import { Api } from '@/common/StandardApi';
import { ApiURL } from '@/config/config';

class ProjectAPI {
	_api: Api;

	constructor() {
		this._api = new Api(ApiURL);
	}
	async getProjects() {
		return await this._api.get(
			`projects`,
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}
	async getProject(id: string) {
		return await this._api.get(
			`projects/${id}`,
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}
	async createProject(body: { name: string; description: string }) {
		return await this._api.post(
			`projects`,
			JSON.stringify(body),
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}
	async updateProject(
		id: string,
		body: {
			name: string;
			description: string;
		},
	) {
		return await this._api.put(
			`projects/${id}`,
			JSON.stringify(body),
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}
	async deleteProject(id: string) {
		return await this._api.delete(
			`projects/${id}`,
			null,
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	async addProjectMembers(id: string, userIds: string[]) {
		return await this._api.post(
			`projects/${id}/members`,
			JSON.stringify({ userIds }),
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	async removeProjectMembers(id: string, userIds: string[]) {
		return await this._api.delete(
			`projects/${id}/members`,
			JSON.stringify({ userIds }),
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}
}

export { ProjectAPI };
