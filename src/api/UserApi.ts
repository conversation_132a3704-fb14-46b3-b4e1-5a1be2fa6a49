import { CommonFunction } from '@/common/Function/Function';
import { Api } from '@/common/StandardApi';
import { ApiURL } from '@/config/config';

export interface User {
	id: string;
	fullName: string;
	email: string;
	avatar?: string;
	role: 'ADMIN' | 'USER';
	status: 'ACTIVE' | 'INACTIVE' | 'REJECTED';
	createdAt?: string;
	updatedAt?: string;
}

export interface GetUsersQueryParams {
	page?: number;
	limit?: number;
	search?: string;
	role?: string;
	status?: 'ACTIVE' | 'INACTIVE' | 'REJECTED';
	sortBy?: string;
	sortOrder?: 'asc' | 'desc';
}

export interface CreateUserDto {
	fullName: string;
	email: string;
	password: string;
	role?: string;
}

export interface UpdateUserDto {
	fullName?: string;
	email?: string;
	avatar?: string;
}

export interface UpdateUserRoleDto {
	role: string;
}

export interface UserStatsResponse {
	totalProjects?: number;
	totalContributions?: number;
	joinedAt?: string;
	lastActivity?: string;
}

class UserAPI {
	_api: Api;

	constructor() {
		this._api = new Api(ApiURL);
	}

	async createUser(createUserDto: CreateUserDto) {
		return await this._api.post(
			`users`,
			JSON.stringify(createUserDto),
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	async getAllUsers(queryParams: GetUsersQueryParams = {}) {
		const urlParams = new URLSearchParams();

		if (queryParams.page) {
			urlParams.append('page', queryParams.page.toString());
		}
		if (queryParams.limit) {
			urlParams.append('limit', queryParams.limit.toString());
		}
		if (queryParams.search) {
			urlParams.append('search', queryParams.search);
		}
		if (queryParams.role) {
			urlParams.append('role', queryParams.role);
		}
		if (queryParams.status) {
			urlParams.append('status', queryParams.status);
		}
		if (queryParams.sortBy) {
			urlParams.append('sortBy', queryParams.sortBy);
		}
		if (queryParams.sortOrder) {
			urlParams.append('sortOrder', queryParams.sortOrder);
		}

		const url = `users${
			urlParams.toString() ? '?' + urlParams.toString() : ''
		}`;

		return await this._api.get(
			url,
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	async getUserById(id: string) {
		return await this._api.get(
			`users/${id}`,
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	async getUserStats(id: string) {
		return await this._api.get(
			`users/${id}/stats`,
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	async updateUser(id: string, updateUserDto: UpdateUserDto) {
		return await this._api.put(
			`users/${id}`,
			JSON.stringify(updateUserDto),
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	async updateUserRole(id: string, updateRoleDto: UpdateUserRoleDto) {
		return await this._api.patch(
			`users/${id}/role`,
			JSON.stringify(updateRoleDto),
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	async toggleUserStatus(id: string) {
		return await this._api.patch(
			`users/${id}/toggle-status`,
			null,
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	async deleteUser(id: string) {
		return await this._api.delete(
			`users/${id}`,
			null,
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	async getCurrentUser() {
		return await this._api.get(
			`users/me`,
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	async searchUsers(
		params: {
			query?: string;
			limit?: number;
			excludeProjectMembers?: string;
		} = {},
	) {
		const queryParams: GetUsersQueryParams = {
			search: params.query,
			limit: params.limit,
		};

		return this.getAllUsers(queryParams);
	}
}

export { UserAPI };
