import { CommonFunction } from '@/common/Function/Function';
import { Api } from '@/common/StandardApi';
import { ApiURL } from '@/config/config';

interface CreateInvitationRequest {
	email: string;
}

interface InvitationListItem {
	id: string;
	email: string;
	role: 'USER' | 'ADMIN';
	status: 'PENDING' | 'ACCEPTED' | 'REJECTED';
	createdAt: string;
	expiresAt: string;
	createdBy: {
		id: string;
		fullName: string;
		email: string;
	};
}

class InvitationApi {
	_api: Api;
	appOrigin: string;

	constructor(appOrigin: string = 'BO') {
		this._api = new Api(ApiURL);
		this.appOrigin = appOrigin;
	}

	async createInvitation(body: CreateInvitationRequest) {
		return await this._api.post(
			`invitation/create`,
			JSON.stringify(body),
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	async getAllInvitations() {
		return await this._api.get(
			`invitation`,
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	async acceptInvitation(body: {
		token: string;
		email: string;
		password: string;
		fullName: string;
	}) {
		return await this._api.post(
			`invitation/accept`,
			JSON.stringify(body),
			CommonFunction.createHeaders({
				withToken: false,
			}),
		);
	}

	async getInvitation(token: string) {
		return await this._api.get(
			`invitation/check-token/${token}`,
			CommonFunction.createHeaders({
				withToken: false,
			}),
		);
	}

	async deleteInvitation(id: string) {
		return await this._api.delete(
			`invitation/${id}`,
			null,
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}
}

export type { CreateInvitationRequest, InvitationListItem };
export default InvitationApi;
