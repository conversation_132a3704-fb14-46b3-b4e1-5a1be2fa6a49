import { CommonFunction } from '@/common/Function/Function';
import { Api } from '@/common/StandardApi';
import { ApiURL } from '@/config/config';
import { getAuthStore } from '@/providers/auth-store-provider';

export interface CreateGenerationRequest {
	name: string;
	type: 'UI' | 'DOCUMENTATION';
	initialPrompt: string;
	projectId: string;
	promptHistoryId?: string;
}

export interface AddMessageRequest {
	content: string;
	inputData?: Record<string, unknown>;
}

export interface GenerationResponse {
	id: string;
	name: string;
	type: 'UI' | 'DOCUMENTATION';
	prompt: string;
	initialPrompt: string;
	status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
	result: string | null;
	currentResult: string | null;
	metadata: {
		model?: string;
		processingTime?: number;
		tokensGenerated?: number;
		framework?: string;
		styling?: string;
		language?: string;
		error?: boolean;
		errorMessage?: string;
	} | null;
	projectId: string;
	createdById: string;
	createdAt: string;
	updatedAt: string;
	// These will be populated by the API with expanded data
	project?: {
		id: string;
		name: string;
		description?: string;
		status?: 'ACTIVE' | 'INACTIVE' | 'ARCHIVED';
	};
	createdBy?: {
		id: string;
		email: string;
		fullName?: string;
	};
	promptHistory?: {
		id: string;
		prompt: string;
		description?: string;
		tags: string[];
		usageCount: number;
	};
}

export interface ConversationMessage {
	id: string;
	generationId: string;
	role: 'USER' | 'ASSISTANT' | 'SYSTEM';
	content: string;
	inputData: Record<string, unknown> | null;
	outputData: Record<string, unknown> | null;
	messageIndex: number;
	status: 'SENT' | 'DELIVERED' | 'FAILED';
	createdAt: string;
	updatedAt: string;
	createdBy?: {
		id: string;
		email: string;
		name: string;
	};
}

export interface ConversationHistoryResponse {
	messages: ConversationMessage[];
	pagination: {
		page: number;
		limit: number;
		total: number;
		totalPages: number;
	};
}

class GenerationAPI {
	_api: Api;

	constructor() {
		this._api = new Api(ApiURL);
	}

	/**
	 * Create a new conversational generation
	 */
	async createConversationalGeneration(body: CreateGenerationRequest) {
		return await this._api.post(
			`generations/conversational`,
			JSON.stringify(body),
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	/**
	 * Create a conversational generation for a specific project
	 */
	async createProjectConversationalGeneration(
		projectId: string,
		body: Omit<CreateGenerationRequest, 'projectId'>,
	) {
		return await this._api.post(
			`projects/${projectId}/generations/conversational`,
			JSON.stringify(body),
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	/**
	 * Create a legacy generation for a project (backward compatibility)
	 */
	async createLegacyGeneration(
		projectId: string,
		body: {
			type: 'UI' | 'DOCUMENTATION';
			prompt: string;
			promptHistoryId?: string;
		},
	) {
		return await this._api.post(
			`projects/${projectId}/generations`,
			JSON.stringify(body),
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	/**
	 * Add a message to an existing conversation
	 */
	async addMessage(generationId: string, body: AddMessageRequest) {
		return await this._api.post(
			`generations/${generationId}/messages`,
			JSON.stringify(body),
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	/**
	 * Get conversation history for a generation
	 */
	async getConversationHistory(
		generationId: string,
		params: {
			page?: number;
			limit?: number;
		} = {},
	) {
		const searchParams = new URLSearchParams();
		if (params.page) searchParams.append('page', params.page.toString());
		if (params.limit) searchParams.append('limit', params.limit.toString());

		const queryString = searchParams.toString();
		const endpoint = `generations/${generationId}/conversation${
			queryString ? `?${queryString}` : ''
		}`;

		return await this._api.get(
			endpoint,
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	/**
	 * Get the current result of a generation
	 */
	async getGenerationResult(generationId: string) {
		return await this._api.get(
			`generations/${generationId}/result`,
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	/**
	 * Get a specific generation by ID
	 */
	async getGeneration(generationId: string) {
		return await this._api.get(
			`generations/${generationId}`,
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	/**
	 * Get all generations for a project
	 */
	async getProjectGenerations(
		projectId: string,
		params: {
			page?: number;
			limit?: number;
			type?: 'UI' | 'DOCUMENTATION';
			status?: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
		} = {},
	) {
		const searchParams = new URLSearchParams();
		if (params.page) searchParams.append('page', params.page.toString());
		if (params.limit) searchParams.append('limit', params.limit.toString());
		if (params.type) searchParams.append('type', params.type);
		if (params.status) searchParams.append('status', params.status);

		const queryString = searchParams.toString();
		const endpoint = `projects/${projectId}/generations${
			queryString ? `?${queryString}` : ''
		}`;

		return await this._api.get(
			endpoint,
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	/**
	 * Get all generations for the current user
	 */
	async getUserGenerations(
		params: {
			page?: number;
			limit?: number;
			type?: 'UI' | 'DOCUMENTATION';
			status?: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
		} = {},
	) {
		const searchParams = new URLSearchParams();
		if (params.page) searchParams.append('page', params.page.toString());
		if (params.limit) searchParams.append('limit', params.limit.toString());
		if (params.type) searchParams.append('type', params.type);
		if (params.status) searchParams.append('status', params.status);

		const queryString = searchParams.toString();
		const endpoint = `generations${queryString ? `?${queryString}` : ''}`;

		return await this._api.get(
			endpoint,
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	/**
	 * Delete a generation
	 */
	async deleteGeneration(generationId: string) {
		return await this._api.delete(
			`generations/${generationId}`,
			null,
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	/**
	 * Update generation metadata
	 */
	async updateGeneration(
		generationId: string,
		body: {
			name?: string;
			metadata?: Record<string, unknown>;
		},
	) {
		return await this._api.put(
			`generations/${generationId}`,
			JSON.stringify(body),
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	/**
	 * Stream real-time updates for a generation using Server-Sent Events
	 * Uses custom implementation to support Authorization headers
	 */
	createGenerationStream(generationId: string): EventSource {
		const authStore = getAuthStore();
		const token = authStore.getState().accessToken;

		// Use the correct API base URL for SSE endpoint (without query token)
		const url = `${ApiURL}generations/${generationId}/events`;

		console.log('🔗 Connecting to SSE with headers:', url);

		// Create custom EventSource-like object that supports headers
		return this.createCustomEventSource(url, {
			Authorization: `Bearer ${token}`,
			Accept: 'text/event-stream',
			'Cache-Control': 'no-cache',
		});
	}

	/**
	 * Custom EventSource implementation that supports headers
	 */
	private createCustomEventSource(
		url: string,
		headers: Record<string, string>,
	): EventSource {
		// Create a mutable state object
		const state = {
			readyState: 0, // CONNECTING
		};

		const eventSource = {
			get readyState() {
				return state.readyState;
			},
			url,
			withCredentials: false,
			onopen: null as ((event: Event) => void) | null,
			onmessage: null as ((event: MessageEvent) => void) | null,
			onerror: null as ((event: Event) => void) | null,
			close: () => {
				if (controller) {
					controller.abort();
				}
				state.readyState = 2; // CLOSED
			},
			addEventListener: (type: string, listener: EventListener) => {
				if (type === 'open')
					eventSource.onopen = listener as (event: Event) => void;
				if (type === 'message')
					eventSource.onmessage = listener as (event: MessageEvent) => void;
				if (type === 'error')
					eventSource.onerror = listener as (event: Event) => void;
			},
			removeEventListener: () => {
				// Not implemented for this use case
			},
			dispatchEvent: () => false,
			CONNECTING: 0,
			OPEN: 1,
			CLOSED: 2,
		} as unknown as EventSource;

		let controller: AbortController | null = null;

		// Start the connection
		const connect = async () => {
			try {
				controller = new AbortController();

				const response = await fetch(url, {
					method: 'GET',
					headers,
					signal: controller.signal,
				});

				if (!response.ok) {
					throw new Error(`HTTP ${response.status}: ${response.statusText}`);
				}

				state.readyState = 1; // OPEN
				if (eventSource.onopen) {
					eventSource.onopen(new Event('open'));
				}

				const reader = response.body?.getReader();
				if (!reader) {
					throw new Error('No response body');
				}

				const decoder = new TextDecoder();
				let buffer = '';

				try {
					while (true) {
						const { done, value } = await reader.read();

						if (done) break;

						buffer += decoder.decode(value, { stream: true });
						const lines = buffer.split('\n');
						buffer = lines.pop() || '';

						for (const line of lines) {
							if (line.startsWith('data: ')) {
								const data = line.slice(6);
								if (eventSource.onmessage) {
									eventSource.onmessage(new MessageEvent('message', { data }));
								}
							}
						}
					}
				} catch (readerError) {
					// Check if this is an abort error
					if (
						readerError instanceof Error &&
						readerError.name === 'AbortError'
					) {
						console.log('SSE stream was aborted (expected during cleanup)');
						return; // Don't treat abort as an error
					}
					throw readerError; // Re-throw other errors
				} finally {
					// Always close the reader
					try {
						reader.releaseLock();
					} catch {
						// Ignore errors when releasing lock
					}
				}

				// Stream ended naturally
				state.readyState = 2; // CLOSED
			} catch (error) {
				// Only log and handle non-abort errors
				if (error instanceof Error && error.name === 'AbortError') {
					console.log('SSE connection was aborted (expected during cleanup)');
					state.readyState = 2; // CLOSED
					return;
				}

				console.error('SSE connection error:', error);
				state.readyState = 2; // CLOSED
				if (eventSource.onerror) {
					eventSource.onerror(new Event('error'));
				}
			}
		};

		connect();
		return eventSource;
	}
}

export { GenerationAPI };
