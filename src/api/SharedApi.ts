import { CommonFunction } from '@/common/Function/Function';
import { Api } from '@/common/StandardApi';
import { ApiURL } from '@/config/config';

class SharedAPI {
	_api: Api;

	constructor() {
		this._api = new Api(ApiURL);
	}

	async getFormResponseById(id: string) {
		return this._api.get(
			`shared/form/one/${id}`,
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}
}
export { SharedAPI };
