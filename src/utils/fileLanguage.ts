/**
 * Utility functions for detecting and handling file languages in Monaco Editor
 */

export interface LanguageConfig {
	monacoLanguage: string;
	displayName: string;
	extensions: string[];
	isReact?: boolean;
	isTypeScript?: boolean;
}

/**
 * Language configuration mapping
 */
export const LANGUAGE_CONFIGS: Record<string, LanguageConfig> = {
	typescriptreact: {
		monacoLanguage: 'typescriptreact',
		displayName: 'TypeScript React',
		extensions: ['tsx'],
		isReact: true,
		isTypeScript: true,
	},
	typescript: {
		monacoLanguage: 'typescript',
		displayName: 'TypeScript',
		extensions: ['ts'],
		isTypeScript: true,
	},
	javascriptreact: {
		monacoLanguage: 'javascriptreact',
		displayName: 'JavaScript React',
		extensions: ['jsx'],
		isReact: true,
	},
	javascript: {
		monacoLanguage: 'javascript',
		displayName: 'JavaScript',
		extensions: ['js', 'mjs'],
	},
	json: {
		monacoLanguage: 'json',
		displayName: 'JSON',
		extensions: ['json'],
	},
	css: {
		monacoLanguage: 'css',
		displayName: 'CSS',
		extensions: ['css'],
	},
	html: {
		monacoLanguage: 'html',
		displayName: 'HTML',
		extensions: ['html', 'htm'],
	},
	markdown: {
		monacoLanguage: 'markdown',
		displayName: 'Markdown',
		extensions: ['md', 'markdown'],
	},
};

/**
 * Detects Monaco Editor language from file extension
 */
export const getMonacoLanguageFromExtension = (filename: string): string => {
	const extension = filename.split('.').pop()?.toLowerCase();
	if (!extension) return 'typescript';

	// Find language config by extension
	for (const config of Object.values(LANGUAGE_CONFIGS)) {
		if (config.extensions.includes(extension)) {
			return config.monacoLanguage;
		}
	}

	return 'typescript'; // Default fallback
};

/**
 * Detects Monaco Editor language from file path or name
 */
export const getMonacoLanguageFromPath = (path: string): string => {
	const filename = path.split('/').pop() || path;
	return getMonacoLanguageFromExtension(filename);
};

/**
 * Gets language configuration by Monaco language ID
 */
export const getLanguageConfig = (monacoLanguage: string): LanguageConfig | null => {
	return LANGUAGE_CONFIGS[monacoLanguage] || null;
};

/**
 * Checks if a language supports React/JSX syntax
 */
export const isReactLanguage = (monacoLanguage: string): boolean => {
	const config = getLanguageConfig(monacoLanguage);
	return config?.isReact || false;
};

/**
 * Checks if a language supports TypeScript syntax
 */
export const isTypeScriptLanguage = (monacoLanguage: string): boolean => {
	const config = getLanguageConfig(monacoLanguage);
	return config?.isTypeScript || false;
};

/**
 * Gets display name for a Monaco language
 */
export const getLanguageDisplayName = (monacoLanguage: string): string => {
	const config = getLanguageConfig(monacoLanguage);
	return config?.displayName || monacoLanguage;
};

/**
 * Gets appropriate file icon based on language
 */
export const getLanguageIcon = (monacoLanguage: string): string => {
	const iconMap: Record<string, string> = {
		typescriptreact: '⚛️',
		typescript: '🔷',
		javascriptreact: '⚛️',
		javascript: '🟨',
		json: '📄',
		css: '🎨',
		html: '🌐',
		markdown: '📝',
	};

	return iconMap[monacoLanguage] || '📄';
};

/**
 * Comprehensive language detection with fallbacks
 */
export const detectFileLanguage = (
	filename: string,
	content?: string,
	fallbackLanguage?: string,
): string => {
	// Primary detection from extension
	let language = getMonacoLanguageFromExtension(filename);

	// If we got a generic result, try content-based detection
	if (language === 'typescript' && content) {
		// Check for JSX patterns in content
		const hasJSX = /<[A-Z][a-zA-Z0-9]*/.test(content) || /<\/[A-Z][a-zA-Z0-9]*>/.test(content);
		const hasReactImport = /import.*React/.test(content);
		
		if (hasJSX || hasReactImport) {
			// Determine if it's TypeScript or JavaScript based on extension
			const ext = filename.split('.').pop()?.toLowerCase();
			if (ext === 'tsx') {
				language = 'typescriptreact';
			} else if (ext === 'jsx') {
				language = 'javascriptreact';
			}
		}
	}

	return language || fallbackLanguage || 'typescript';
};
