/**
 * Utility functions for handling markdown content
 */

/**
 * Strips markdown code blocks from text content
 * Handles various markdown code block formats:
 * - ```language\ncode\n```
 * - ```\ncode\n```
 * - ``` language\ncode\n```
 * - ```language code ```
 * NOTE: Preserves individual backticks (`) to avoid breaking code syntax
 */
export const stripMarkdownCodeBlocks = (text: string): string => {
	if (!text) return text;

	console.log('Original text:', text);

	// More comprehensive regex to handle various markdown code block formats
	let strippedText = text;

	// Pattern 1: Standard code blocks with optional language and newlines
	// Matches: ```optional-language optional-newline content ```
	const codeBlockRegex1 = /```[\w]*\s*\n?([\s\S]*?)\n?\s*```/g;
	strippedText = strippedText.replace(codeBlockRegex1, '$1');

	// Pattern 2: Code blocks without newlines (inline style)
	// Matches: ```language content```
	const codeBlockRegex2 = /```[\w]*\s+(.*?)```/g;
	strippedText = strippedText.replace(codeBlockRegex2, '$1');

	// Pattern 3: Simple code blocks without language
	// Matches: ```content```
	const codeBlockRegex3 = /```([^`]+)```/g;
	strippedText = strippedText.replace(codeBlockRegex3, '$1');

	// Pattern 4: Handle any remaining triple backticks (but preserve single backticks)
	strippedText = strippedText.replace(/```/g, '');

	// DO NOT strip individual backticks - they're important for code syntax
	// Individual backticks (`) are preserved to maintain code integrity

	// Clean up extra whitespace
	strippedText = strippedText.trim();

	console.log('Stripped text:', strippedText);
	return strippedText;
};

/**
 * Extracts code from markdown code blocks with language detection
 * Returns both the cleaned code and detected language
 */
export const extractCodeFromMarkdown = (
	text: string,
): { code: string; language?: string } => {
	if (!text) return { code: text };

	// Try to extract language from code block
	const codeBlockWithLangRegex = /```(\w+)\s*\n?([\s\S]*?)\n?\s*```/;
	const match = text.match(codeBlockWithLangRegex);

	if (match) {
		const [, language, code] = match;
		return {
			code: code.trim(),
			language: language.toLowerCase(),
		};
	}

	// Fallback to regular stripping
	return {
		code: stripMarkdownCodeBlocks(text),
	};
};

/**
 * Checks if text contains markdown code blocks
 */
export const hasMarkdownCodeBlocks = (text: string): boolean => {
	if (!text) return false;
	return /```[\s\S]*?```/.test(text);
};

/**
 * Wraps code in markdown code blocks with optional language
 */
export const wrapInMarkdownCodeBlock = (
	code: string,
	language?: string,
): string => {
	const lang = language || '';
	return `\`\`\`${lang}\n${code}\n\`\`\``;
};
