import type { WebContainer } from '@webcontainer/api';

// Global WebContainer singleton manager
class WebContainerSingleton {
	private static instance: WebContainer | null = null;
	private static isBooting: boolean = false;
	private static bootPromise: Promise<WebContainer> | null = null;

	/**
	 * Get or create the WebContainer instance
	 */
	static async getInstance(): Promise<WebContainer> {
		// If we already have an instance, return it
		if (this.instance) {
			console.log('♻️ Reusing existing WebContainer instance');
			return this.instance;
		}

		// If we're already booting, wait for that to complete
		if (this.isBooting && this.bootPromise) {
			console.log('⏳ WebContainer already booting, waiting...');
			return this.bootPromise;
		}

		// Check if there's a global instance from another hook
		if (
			typeof window !== 'undefined' &&
			(window as unknown as Record<string, unknown>).__webcontainer
		) {
			console.log('♻️ Found existing global WebContainer instance');
			this.instance = (window as unknown as Record<string, unknown>)
				.__webcontainer as WebContainer;
			return this.instance;
		}

		// Boot a new instance
		console.log('🚀 Booting new WebContainer instance...');
		this.isBooting = true;

		this.bootPromise = this.bootNewInstance();

		try {
			this.instance = await this.bootPromise;
			console.log('✅ WebContainer singleton ready');
			return this.instance;
		} finally {
			this.isBooting = false;
			this.bootPromise = null;
		}
	}

	/**
	 * Boot a new WebContainer instance
	 */
	private static async bootNewInstance(): Promise<WebContainer> {
		// Dynamic import to avoid loading WebContainer resources until needed
		const { WebContainer } = await import('@webcontainer/api');

		// Double-check that no other instance was created while we were importing
		if (
			typeof window !== 'undefined' &&
			(window as unknown as Record<string, unknown>).__webcontainer
		) {
			console.log('♻️ Another instance was created during import, using that');
			return (window as unknown as Record<string, unknown>)
				.__webcontainer as WebContainer;
		}

		const instance = await WebContainer.boot();

		// Store globally to prevent multiple instances
		if (typeof window !== 'undefined') {
			(window as unknown as Record<string, unknown>).__webcontainer = instance;
		}

		return instance;
	}

	/**
	 * Check if WebContainer is available
	 */
	static isAvailable(): boolean {
		return (
			this.instance !== null ||
			(typeof window !== 'undefined' &&
				!!(window as unknown as Record<string, unknown>).__webcontainer)
		);
	}

	/**
	 * Get the current instance without booting
	 */
	static getCurrentInstance(): WebContainer | null {
		if (this.instance) {
			return this.instance;
		}

		if (
			typeof window !== 'undefined' &&
			(window as unknown as Record<string, unknown>).__webcontainer
		) {
			this.instance = (window as unknown as Record<string, unknown>)
				.__webcontainer as WebContainer;
			return this.instance;
		}

		return null;
	}

	/**
	 * Reset the singleton (for testing or cleanup)
	 */
	static reset(): void {
		console.log('🧹 Resetting WebContainer singleton');
		this.instance = null;
		this.isBooting = false;
		this.bootPromise = null;

		if (typeof window !== 'undefined') {
			delete (window as unknown as Record<string, unknown>).__webcontainer;
		}
	}

	/**
	 * Check if WebContainer is currently booting
	 */
	static isBooting(): boolean {
		return this.isBooting;
	}
}

export { WebContainerSingleton };
