import { useState, useCallback, useMemo } from 'react';
import InvitationApi, {
	CreateInvitationRequest,
	InvitationListItem,
} from '@/api/InvitationApi';
import {
	isApiSuccess,
	extractApiData,
	getApiErrorMessage,
} from '@/common/utils/apiResponse';

interface UseInvitationsReturn {
	invitations: InvitationListItem[];
	loading: boolean;
	error: string | null;
	createInvitation: (data: CreateInvitationRequest) => Promise<boolean>;
	getAllInvitations: () => Promise<void>;
	deleteInvitation: (id: string) => Promise<boolean>;
	clearError: () => void;
}

export const useInvitations = (): UseInvitationsReturn => {
	const [invitations, setInvitations] = useState<InvitationListItem[]>([]);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const invitationApi = useMemo(() => new InvitationApi(), []);

	const clearError = useCallback(() => {
		setError(null);
	}, []);

	const createInvitation = useCallback(
		async (data: CreateInvitationRequest): Promise<boolean> => {
			setLoading(true);
			setError(null);

			try {
				const response = await invitationApi.createInvitation(data);

				if (isApiSuccess(response)) {
					const newInvitation = extractApiData(response) as InvitationListItem;
					if (newInvitation) {
						setInvitations((prev) => [newInvitation, ...prev]);
					}
					return true;
				} else {
					const errorMessage = getApiErrorMessage(response);
					setError(errorMessage);
					return false;
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to create invitation';
				setError(errorMessage);
				return false;
			} finally {
				setLoading(false);
			}
		},
		[invitationApi],
	);

	const getAllInvitations = useCallback(async (): Promise<void> => {
		setLoading(true);
		setError(null);

		try {
			const response = await invitationApi.getAllInvitations();

			if (isApiSuccess(response)) {
				const data = extractApiData(response) as InvitationListItem[];
				setInvitations(data || []);
			} else {
				const errorMessage = getApiErrorMessage(response);
				setError(errorMessage);
				setInvitations([]);
			}
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : 'Failed to fetch invitations';
			setError(errorMessage);
			setInvitations([]);
		} finally {
			setLoading(false);
		}
	}, [invitationApi]);

	const deleteInvitation = useCallback(
		async (id: string): Promise<boolean> => {
			setLoading(true);
			setError(null);

			try {
				const response = await invitationApi.deleteInvitation(id);

				if (isApiSuccess(response)) {
					// Remove the deleted invitation from the local state
					setInvitations((prev) => prev.filter((inv) => inv.id !== id));
					return true;
				} else {
					const errorMessage = getApiErrorMessage(response);
					setError(errorMessage);
					return false;
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to delete invitation';
				setError(errorMessage);
				return false;
			} finally {
				setLoading(false);
			}
		},
		[invitationApi],
	);

	return {
		invitations,
		loading,
		error,
		createInvitation,
		getAllInvitations,
		deleteInvitation,
		clearError,
	};
};
