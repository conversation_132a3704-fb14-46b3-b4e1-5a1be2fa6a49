import { useCallback, useEffect } from 'react';
import {
	useGenerationStore,
	validateGenerationStore,
} from '@/stores/generationStore';
import { useWebContainer } from '@/hooks/useWebContainer';
import { createFileSystemFromCode } from '@/utils/webcontainer';
import { stripMarkdownCodeBlocks } from '@/utils/markdown';
import { WebContainerSingleton } from '@/utils/webcontainer-singleton';

/**
 * Hook for integrating WebContainer with the generation store
 */
export const useWebContainerIntegration = (enabled: boolean = true) => {
	// Get store access first (hooks must be called unconditionally)
	const generationStore = useGenerationStore();

	// Destructure store with safe defaults
	const {
		webContainer: webContainerState = { hasInitialized: false },
		editableContent = {},
		getFileContent = () => '',
		canSyncToWebContainer = () => false,
		syncToWebContainer = () => {},
		syncAllToWebContainer = () => {},
		setWebContainerBooting = () => {},
		setWebContainerReady = () => {},
		setWebContainerError = () => {},
		setWebContainerInstalling = () => {},
		setWebContainerStarting = () => {},
		setWebContainerInitialized = () => {},
	} = generationStore || {};

	// Always call useWebContainer hook (hooks must be called unconditionally)
	const {
		webcontainer,
		isBooting,
		isReady,
		error,
		url,
		mountFiles,
		writeFile,
		installDependencies,
		startDevServer,
		stopDevServer,
		isInstalling,
		isStarting,
	} = useWebContainer(enabled && validateGenerationStore(generationStore));

	// Check if integration is actually enabled
	const isIntegrationEnabled =
		enabled && validateGenerationStore(generationStore);

	// Sync WebContainer state with store
	useEffect(() => {
		if (isIntegrationEnabled && isBooting) setWebContainerBooting();
	}, [isIntegrationEnabled, isBooting, setWebContainerBooting]);

	useEffect(() => {
		if (isIntegrationEnabled && isReady) setWebContainerReady(url || undefined);
	}, [isIntegrationEnabled, isReady, url, setWebContainerReady]);

	useEffect(() => {
		if (isIntegrationEnabled && error) setWebContainerError(error);
	}, [isIntegrationEnabled, error, setWebContainerError]);

	useEffect(() => {
		if (isIntegrationEnabled && isInstalling) setWebContainerInstalling();
	}, [isIntegrationEnabled, isInstalling, setWebContainerInstalling]);

	useEffect(() => {
		if (isIntegrationEnabled && isStarting) setWebContainerStarting();
	}, [isIntegrationEnabled, isStarting, setWebContainerStarting]);

	// Get WebContainer file path from file ID
	const getWebContainerPath = useCallback((fileId: string): string | null => {
		const pathMap: Record<string, string> = {
			'app-tsx': 'src/App.tsx',
			'index-css': 'src/index.css',
			'main-tsx': 'src/main.tsx',
			'package-json': 'package.json',
			'vite-config': 'vite.config.ts',
			'tailwind-config': 'tailwind.config.js',
			'index-html': 'index.html',
		};
		return pathMap[fileId] || null;
	}, []);

	// Initialize WebContainer with current files
	const initializeWebContainer = useCallback(async () => {
		if (!isIntegrationEnabled || !isReady) return false;

		try {
			// Get the main App.tsx content
			const appContent = getFileContent('app-tsx');
			const cleanedCode = stripMarkdownCodeBlocks(appContent);

			// Create file system structure
			const fileSystem = createFileSystemFromCode(cleanedCode);

			// Mount files to WebContainer
			await mountFiles(fileSystem);

			// Install dependencies
			await installDependencies();

			// Start dev server
			await startDevServer();

			setWebContainerInitialized();
			return true;
		} catch (err) {
			const errorMessage =
				err instanceof Error
					? err.message
					: 'Failed to initialize WebContainer';
			setWebContainerError(errorMessage);
			return false;
		}
	}, [
		isReady,
		getFileContent,
		mountFiles,
		installDependencies,
		startDevServer,
		setWebContainerInitialized,
		setWebContainerError,
	]);

	// Update specific file in WebContainer
	const updateFileInWebContainer = useCallback(
		async (fileId: string) => {
			if (!isIntegrationEnabled || !isReady || !webcontainer) return false;

			try {
				const content = getFileContent(fileId);
				const filePath = getWebContainerPath(fileId);

				if (filePath && content) {
					await writeFile(filePath, content);
					syncToWebContainer(fileId);
					return true;
				}
			} catch (err) {
				console.error('Failed to update file in WebContainer:', err);
			}
			return false;
		},
		[
			isReady,
			webcontainer,
			getFileContent,
			writeFile,
			syncToWebContainer,
			getWebContainerPath,
		],
	);

	// Update all files in WebContainer
	const updateAllFilesInWebContainer = useCallback(async () => {
		if (!isIntegrationEnabled || !isReady || !webcontainer) return false;

		try {
			// Update main App.tsx file
			const appContent = getFileContent('app-tsx');
			if (appContent) {
				await writeFile('src/App.tsx', appContent);
			}

			// Update CSS file if edited
			const cssContent = editableContent['index-css'];
			if (cssContent) {
				await writeFile('src/index.css', cssContent);
			}

			syncAllToWebContainer();
			return true;
		} catch (err) {
			console.error('Failed to update files in WebContainer:', err);
		}
		return false;
	}, [
		isReady,
		webcontainer,
		getFileContent,
		editableContent,
		writeFile,
		syncAllToWebContainer,
	]);

	// Auto-sync when files change (now handled by useDebouncedSync hook)
	const autoSyncToWebContainer = useCallback(
		async (fileId: string) => {
			// This is now a legacy method - the actual auto-sync is handled by useDebouncedSync
			// Keep for backward compatibility but recommend using useDebouncedSync directly
			if (webContainerState.hasInitialized && canSyncToWebContainer()) {
				return await updateFileInWebContainer(fileId);
			}
			return false;
		},
		[
			webContainerState.hasInitialized,
			canSyncToWebContainer,
			updateFileInWebContainer,
		],
	);

	// Restart WebContainer preview
	const restartPreview = useCallback(async () => {
		if (!isIntegrationEnabled || !isReady) return false;

		try {
			stopDevServer();
			await updateAllFilesInWebContainer();
			await startDevServer();
			return true;
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : 'Failed to restart preview';
			setWebContainerError(errorMessage);
			return false;
		}
	}, [
		isReady,
		stopDevServer,
		updateAllFilesInWebContainer,
		startDevServer,
		setWebContainerError,
	]);

	// Check if WebContainer is ready for operations
	const isWebContainerOperational = useCallback(() => {
		return (
			isIntegrationEnabled &&
			isReady &&
			webContainerState.hasInitialized &&
			!error
		);
	}, [isIntegrationEnabled, isReady, webContainerState.hasInitialized, error]);

	return {
		// State
		webContainerState,
		isReady,
		isBooting,
		isInstalling,
		isStarting,
		error,
		url,

		// Actions
		initializeWebContainer,
		updateFileInWebContainer,
		updateAllFilesInWebContainer,
		autoSyncToWebContainer,
		restartPreview,
		stopDevServer,

		// Utilities
		isWebContainerOperational,
		canSyncToWebContainer,
		getWebContainerPath,
	};
};
