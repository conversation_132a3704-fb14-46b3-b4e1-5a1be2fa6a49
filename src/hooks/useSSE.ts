import { useState, useEffect, useRef, useCallback } from 'react';
import { GenerationAPI } from '@/api/GenerationApi';

export interface SSEEvent {
	type:
		| 'generation_started'
		| 'generation_progress'
		| 'generation_completed'
		| 'generation_failed';
	data: {
		generationId: string;
		status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
		result?: string;
		currentResult?: string;
		metadata?: any;
		error?: string;
	};
}

interface UseSSEOptions {
	onEvent?: (event: SSEEvent) => void;
	onError?: (error: Error) => void;
	onConnect?: () => void;
	onDisconnect?: () => void;
}

/**
 * Clean SSE hook for real-time generation updates
 * No fallback methods, no timeouts - pure event-driven architecture
 */
export const useSSE = (generationId: string, options: UseSSEOptions = {}) => {
	const [isConnected, setIsConnected] = useState(false);
	const [lastEvent, setLastEvent] = useState<SSEEvent | null>(null);
	const [error, setError] = useState<string | null>(null);

	const eventSourceRef = useRef<EventSource | null>(null);
	const { onEvent, onError, onConnect, onDisconnect } = options;

	const connect = useCallback(() => {
		if (!generationId || eventSourceRef.current) return;

		console.log('🚀 Connecting to SSE for generation:', generationId);

		const eventSource = GenerationAPI.createGenerationStream(generationId);
		eventSourceRef.current = eventSource;

		eventSource.onopen = () => {
			console.log('✅ SSE connected');
			setIsConnected(true);
			setError(null);
			onConnect?.();
		};

		eventSource.onmessage = (event) => {
			try {
				const sseEvent: SSEEvent = JSON.parse(event.data);
				console.log('📨 SSE Event:', sseEvent);

				setLastEvent(sseEvent);
				onEvent?.(sseEvent);
			} catch (err) {
				console.error('Failed to parse SSE event:', err);
				setError('Failed to parse server event');
			}
		};

		eventSource.onerror = (event) => {
			console.error('❌ SSE error:', event);
			setIsConnected(false);

			const errorMsg = 'SSE connection error';
			setError(errorMsg);
			onError?.(new Error(errorMsg));
		};
	}, [generationId, onEvent, onError, onConnect]);

	const disconnect = useCallback(() => {
		if (eventSourceRef.current) {
			console.log('🛑 Disconnecting SSE');
			eventSourceRef.current.close();
			eventSourceRef.current = null;
			setIsConnected(false);
			onDisconnect?.();
		}
	}, [onDisconnect]);

	// Auto-connect when generationId changes
	useEffect(() => {
		if (generationId) {
			connect();
		}

		return () => {
			disconnect();
		};
	}, [generationId, connect, disconnect]);

	return {
		isConnected,
		lastEvent,
		error,
		connect,
		disconnect,
	};
};
