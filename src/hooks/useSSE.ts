import { useState, useEffect, useRef, useCallback } from 'react';
import { GenerationAPI } from '@/api/GenerationApi';

export interface SSEEvent {
	type:
		| 'generation_started'
		| 'generation_progress'
		| 'generation_completed'
		| 'generation_failed';
	data: {
		generationId: string;
		status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
		result?: string;
		currentResult?: string;
		metadata?: any;
		error?: string;
	};
}

interface UseSSEOptions {
	onEvent?: (event: SSEEvent) => void;
	onError?: (error: Error) => void;
	onConnect?: () => void;
	onDisconnect?: () => void;
}

/**
 * Clean SSE hook for real-time generation updates
 * No fallback methods, no timeouts - pure event-driven architecture
 */
export const useSSE = (generationId: string, options: UseSSEOptions = {}) => {
	const [isConnected, setIsConnected] = useState(false);
	const [lastEvent, setLastEvent] = useState<SSEEvent | null>(null);
	const [error, setError] = useState<string | null>(null);

	const eventSourceRef = useRef<EventSource | null>(null);
	const generationAPIRef = useRef<GenerationAPI>(new GenerationAPI());

	// Use refs to store the latest callback functions to avoid dependency issues
	const callbacksRef = useRef(options);
	callbacksRef.current = options;

	// Auto-connect when generationId changes
	useEffect(() => {
		if (!generationId) return;

		// Prevent duplicate connections
		if (eventSourceRef.current) {
			console.log('🛑 Closing existing SSE connection before creating new one');
			eventSourceRef.current.close();
			eventSourceRef.current = null;
			setIsConnected(false);
		}

		console.log('🚀 Connecting to SSE for generation:', generationId);

		const eventSource =
			generationAPIRef.current.createGenerationStream(generationId);
		eventSourceRef.current = eventSource;

		eventSource.onopen = () => {
			console.log('✅ SSE connected');
			setIsConnected(true);
			setError(null);
			callbacksRef.current.onConnect?.();
		};

		eventSource.onmessage = (event: MessageEvent) => {
			try {
				const sseEvent: SSEEvent = JSON.parse(event.data);
				console.log('📨 SSE Event:', sseEvent);

				setLastEvent(sseEvent);
				callbacksRef.current.onEvent?.(sseEvent);
			} catch (err) {
				console.error('Failed to parse SSE event:', err);
				setError('Failed to parse server event');
			}
		};

		eventSource.onerror = (event: Event) => {
			console.error('❌ SSE error:', event);
			setIsConnected(false);

			const errorMsg = 'SSE connection error';
			setError(errorMsg);
			callbacksRef.current.onError?.(new Error(errorMsg));
		};

		// Cleanup function
		return () => {
			console.log('🛑 Cleaning up SSE connection');
			if (eventSourceRef.current) {
				eventSourceRef.current.close();
				eventSourceRef.current = null;
			}
			setIsConnected(false);
			callbacksRef.current.onDisconnect?.();
		};
	}, [generationId]); // Only depend on generationId

	const connect = useCallback(() => {
		// This is now just a manual trigger, the main connection logic is in useEffect
		if (!generationId || eventSourceRef.current) return;
		// The useEffect will handle the actual connection
	}, [generationId]);

	const disconnect = useCallback(() => {
		if (eventSourceRef.current) {
			console.log('🛑 Manually disconnecting SSE');
			eventSourceRef.current.close();
			eventSourceRef.current = null;
			setIsConnected(false);
			callbacksRef.current.onDisconnect?.();
		}
	}, []);

	return {
		isConnected,
		lastEvent,
		error,
		connect,
		disconnect,
	};
};
