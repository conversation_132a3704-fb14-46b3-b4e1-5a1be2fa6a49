import { useCallback } from 'react';
import { useGenerationStore } from '@/stores/generationStore';
import { stripMarkdownCodeBlocks } from '@/utils/markdown';
import type { FileSystemNode } from '@/types/generation';

/**
 * Hook for file operations in the generation workspace
 */
export const useFileOperations = () => {
	const {
		files,
		activeFileId,
		editableContent,
		setFiles,
		clearFiles,
		setActiveFile,
		updateFileContent,
		resetFileContent,
		markFileDirty,
		markFileClean,
		getActiveFile,
		getFileContent,
		isFileDirty,
		getFileByPath,
	} = useGenerationStore();

	const initializeFiles = useCallback(
		(generationResult: string, metadata?: Record<string, unknown>) => {
			const generationType = (metadata as any)?.type || 'UI';

			if (generationType === 'DOCUMENTATION') {
				// For documentation, create a single markdown file without stripping markdown
				const documentationFile: FileSystemNode = {
					id: 'documentation-md',
					name: 'Documentation.md',
					type: 'file',
					language: 'markdown',
					path: 'Documentation.md',
					isEditable: true,
					content: generationResult, // Keep original markdown with ALL formatting
				};

				// Batch the file operations to prevent cascading updates
				clearFiles();
				// Use setTimeout to batch the state updates
				setTimeout(() => {
					setFiles([documentationFile]);
					setActiveFile('documentation-md');
				}, 0);
				return;
			}

			// For UI generations, strip markdown and extract code
			const cleanedCode = stripMarkdownCodeBlocks(generationResult);

			const projectFiles: FileSystemNode[] = [
				// Configuration files
				{
					id: 'package-json',
					name: 'package.json',
					type: 'file',
					language: 'json',
					path: 'package.json',
					isEditable: true,
					content: JSON.stringify(
						{
							name: 'generated-preview',
							private: true,
							version: '0.0.0',
							type: 'module',
							scripts: {
								dev: 'vite',
								build: 'tsc && vite build',
								preview: 'vite preview',
							},
							dependencies: {
								react: '^18.2.0',
								'react-dom': '^18.2.0',
								'lucide-react': '^0.263.1',
							},
							devDependencies: {
								'@vitejs/plugin-react': '^4.2.1',
								autoprefixer: '^10.4.14',
								postcss: '^8.4.24',
								tailwindcss: '^3.3.0',
								typescript: '^5.2.2',
								vite: '^5.2.0',
							},
						},
						null,
						2,
					),
				},
				{
					id: 'vite-config',
					name: 'vite.config.ts',
					type: 'file',
					language: 'typescript',
					path: 'vite.config.ts',
					isEditable: true,
					content: `import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    host: true
  }
})`,
				},
				{
					id: 'tailwind-config',
					name: 'tailwind.config.js',
					type: 'file',
					language: 'javascript',
					path: 'tailwind.config.js',
					isEditable: true,
					content: `/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}`,
				},
				{
					id: 'index-html',
					name: 'index.html',
					type: 'file',
					language: 'html',
					path: 'index.html',
					isEditable: true,
					content: `<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Generated Preview</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>`,
				},
				// Source files
				{
					id: 'src-folder',
					name: 'src/',
					type: 'folder',
					path: 'src',
					isEditable: false,
				},
				{
					id: 'main-tsx',
					name: 'src/main.tsx',
					type: 'file',
					language: 'typescriptreact',
					path: 'src/main.tsx',
					isEditable: true,
					content: `import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)`,
				},
				{
					id: 'index-css',
					name: 'src/index.css',
					type: 'file',
					language: 'css',
					path: 'src/index.css',
					isEditable: true,
					content: `@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}`,
				},
				{
					id: 'app-tsx',
					name: 'src/App.tsx',
					type: 'file',
					language: 'typescriptreact',
					path: 'src/App.tsx',
					isEditable: true,
					content:
						cleanedCode ||
						`import React from 'react';

function App() {
  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold text-gray-800 mb-4">
          Generated Component
        </h1>
        <p className="text-gray-600">
          Your generated code will appear here.
        </p>
      </div>
    </div>
  );
}

export default App;`,
				},
			];

			// Batch the file operations to prevent cascading updates
			clearFiles();
			// Use setTimeout to batch the state updates
			setTimeout(() => {
				setFiles(projectFiles);
			}, 0);
		},
		[setFiles, clearFiles],
	);

	// Copy file content to clipboard
	const copyFileContent = useCallback(
		async (fileId: string) => {
			const content = getFileContent(fileId);
			if (content) {
				try {
					await navigator.clipboard.writeText(content);
					return true;
				} catch (err) {
					console.error('Failed to copy file content:', err);
					return false;
				}
			}
			return false;
		},
		[getFileContent],
	);

	// Get current file content (edited or original)
	const getCurrentFileContent = useCallback(
		(fileId: string) => {
			return (
				editableContent[fileId] ||
				files.find((f) => f.id === fileId)?.content ||
				''
			);
		},
		[editableContent, files],
	);

	// Check if file has unsaved changes
	const hasUnsavedChanges = useCallback(
		(fileId: string) => {
			return isFileDirty(fileId);
		},
		[isFileDirty],
	);

	// Get files by type
	const getFilesByType = useCallback(
		(type: 'file' | 'folder') => {
			return files.filter((f) => f.type === type);
		},
		[files],
	);

	// Get editable files only
	const getEditableFiles = useCallback(() => {
		return files.filter((f) => f.isEditable);
	}, [files]);

	return {
		// State
		files,
		activeFileId,
		activeFile: getActiveFile(),

		// Actions
		initializeFiles,
		setActiveFile,
		updateFileContent,
		resetFileContent,
		markFileDirty,
		markFileClean,
		copyFileContent,

		// Getters
		getFileContent,
		getCurrentFileContent,
		getFileByPath,
		getFilesByType,
		getEditableFiles,
		hasUnsavedChanges,
		isFileDirty,
	};
};
