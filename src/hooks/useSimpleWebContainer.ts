import { useCallback, useEffect } from 'react';
import { useGenerationStore } from '@/stores/generationStore';
import { useWebContainer } from '@/hooks/useWebContainer';
import { createFileSystemFromCode } from '@/utils/webcontainer';
import { stripMarkdownCodeBlocks } from '@/utils/markdown';

/**
 * Simplified WebContainer integration
 * Only runs when there is generated data from SSE
 * No debouncing, no complex sync logic
 */
export const useSimpleWebContainer = (enabled: boolean = true) => {
	const generationStore = useGenerationStore();
	const {
		webContainer: webContainerState,
		editableContent,
		getFileContent,
		setWebContainerBooting,
		setWebContainerReady,
		setWebContainerError,
		setWebContainerInstalling,
		setWebContainerStarting,
		setWebContainerInitialized,
		markFileClean,
	} = generationStore;

	const {
		webcontainer,
		isBooting,
		isReady,
		error,
		url,
		mountFiles,
		writeFile,
		installDependencies,
		startDevServer,
		stopDevServer,
		isInstalling,
		isStarting,
	} = useWebContainer(enabled);

	// Sync WebContainer state with store
	useEffect(() => {
		if (enabled && isBooting) setWebContainerBooting();
	}, [enabled, isBooting, setWebContainerBooting]);

	useEffect(() => {
		if (enabled && isReady) setWebContainerReady(url || undefined);
	}, [enabled, isReady, url, setWebContainerReady]);

	useEffect(() => {
		if (enabled && error) setWebContainerError(error);
	}, [enabled, error, setWebContainerError]);

	useEffect(() => {
		if (enabled && isInstalling) setWebContainerInstalling();
	}, [enabled, isInstalling, setWebContainerInstalling]);

	useEffect(() => {
		if (enabled && isStarting) setWebContainerStarting();
	}, [enabled, isStarting, setWebContainerStarting]);

	// Initialize WebContainer with generated files
	const initializeWithGeneratedFiles = useCallback(async (generatedCode: string) => {
		if (!enabled || !isReady) return false;

		try {
			console.log('🚀 Initializing WebContainer with generated code');
			
			// Clean the generated code
			const cleanedCode = stripMarkdownCodeBlocks(generatedCode);
			
			// Create file system structure
			const fileSystem = createFileSystemFromCode(cleanedCode);
			
			// Mount files to WebContainer
			await mountFiles(fileSystem);
			
			// Install dependencies
			await installDependencies();
			
			// Start dev server
			await startDevServer();
			
			setWebContainerInitialized();
			return true;
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : 'Failed to initialize WebContainer';
			setWebContainerError(errorMessage);
			return false;
		}
	}, [
		enabled,
		isReady,
		mountFiles,
		installDependencies,
		startDevServer,
		setWebContainerInitialized,
		setWebContainerError,
	]);

	// Update specific file in WebContainer
	const updateFile = useCallback(async (fileId: string, content: string) => {
		if (!enabled || !isReady || !webcontainer) return false;

		try {
			const pathMap: Record<string, string> = {
				'app-tsx': 'src/App.tsx',
				'index-css': 'src/index.css',
				'main-tsx': 'src/main.tsx',
				'package-json': 'package.json',
				'vite-config': 'vite.config.ts',
				'tailwind-config': 'tailwind.config.js',
				'index-html': 'index.html',
			};

			const filePath = pathMap[fileId];
			if (filePath && content) {
				await writeFile(filePath, content);
				markFileClean(fileId);
				return true;
			}
		} catch (err) {
			console.error('Failed to update file in WebContainer:', err);
		}
		return false;
	}, [enabled, isReady, webcontainer, writeFile, markFileClean]);

	// Update all edited files in WebContainer
	const updateAllFiles = useCallback(async () => {
		if (!enabled || !isReady || !webcontainer) return false;

		try {
			// Update main App.tsx file
			const appContent = getFileContent('app-tsx');
			if (appContent) {
				await writeFile('src/App.tsx', appContent);
				markFileClean('app-tsx');
			}

			// Update CSS file if edited
			const cssContent = editableContent['index-css'];
			if (cssContent) {
				await writeFile('src/index.css', cssContent);
				markFileClean('index-css');
			}

			return true;
		} catch (err) {
			console.error('Failed to update files in WebContainer:', err);
		}
		return false;
	}, [enabled, isReady, webcontainer, getFileContent, editableContent, writeFile, markFileClean]);

	// Restart WebContainer preview
	const restartPreview = useCallback(async () => {
		if (!enabled || !isReady) return false;

		try {
			stopDevServer();
			await updateAllFiles();
			await startDevServer();
			return true;
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : 'Failed to restart preview';
			setWebContainerError(errorMessage);
			return false;
		}
	}, [enabled, isReady, stopDevServer, updateAllFiles, startDevServer, setWebContainerError]);

	return {
		// State
		webContainerState,
		isReady,
		isBooting,
		isInstalling,
		isStarting,
		error,
		url,

		// Actions
		initializeWithGeneratedFiles,
		updateFile,
		updateAllFiles,
		restartPreview,
		stopDevServer,

		// Utilities
		isOperational: enabled && isReady && webContainerState.hasInitialized && !error,
	};
};
