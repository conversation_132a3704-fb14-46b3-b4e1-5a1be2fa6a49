import { useState } from 'react';
import { useRouter } from 'next/router';
import { AuthAPI } from '@/api/AuthApi';
import { useAuthStore, getAuthStore } from '@/providers/auth-store-provider';
import {
	isApiSuccess,
	extractAuthTokens,
	getApiErrorMessage,
	AuthResponseData,
} from '@/common/utils/apiResponse';

interface LoginCredentials {
	email: string;
	password: string;
}

interface RegisterCredentials {
	fullName: string;
	email: string;
	password: string;
	invitationToken?: string;
}

export const useAuth = () => {
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const router = useRouter();
	const setTokens = useAuthStore((state) => state.setTokens);
	const setUser = useAuthStore((state) => state.setUser);
	const authLogout = useAuthStore((state) => state.logout);

	const clearError = () => setError(null);

	const extractTokens = (response: { data: unknown }) => {
		return extractAuthTokens(response);
	};

	const authenticateUser = async (
		accessToken: string,
		refreshToken: string,
		userData?: AuthResponseData['user'],
	) => {
		console.log('authenticateUser called with:', {
			accessToken: accessToken ? 'present' : 'missing',
			refreshToken: refreshToken ? 'present' : 'missing',
			userData,
		});

		if (
			accessToken &&
			refreshToken &&
			typeof accessToken === 'string' &&
			typeof refreshToken === 'string'
		) {
			try {
				console.log('Setting tokens...');
				await setTokens(accessToken, refreshToken);

				console.log('Tokens set, waiting for store update...');
				await new Promise((resolve) => setTimeout(resolve, 100));

				try {
					const authStore = getAuthStore();
					const currentStoreError = authStore.getState().error;
					console.log('Store error after setting tokens:', currentStoreError);
					if (currentStoreError) {
						console.error('Token validation failed:', currentStoreError);
						setError(currentStoreError);
						return false;
					}
				} catch (storeAccessError) {
					console.warn(
						'Could not access auth store for error checking:',
						storeAccessError,
					);
				}

				if (userData) {
					setUser({
						id: userData.id,
						fullName: userData.fullName || userData.email?.split('@')[0] || '',
						email: userData.email,
						role: userData.role,
						status: userData.status,
					});
				}

				const redirectUrl = router.query.redirect as string;
				const destination =
					redirectUrl &&
					(redirectUrl.startsWith('/projects') ||
						redirectUrl.startsWith('/admin'))
						? redirectUrl
						: '/';

				console.log('Redirecting to:', destination);
				router.push(destination);
				console.log('authenticateUser returning true');
				return true;
			} catch (error) {
				console.error('Error in authenticateUser:', error);
				return false;
			}
		}
		console.log('authenticateUser returning false - invalid tokens');
		return false;
	};

	const login = async (credentials: LoginCredentials) => {
		setIsLoading(true);
		setError(null);

		try {
			const authApi = new AuthAPI();
			const response = await authApi.login(credentials);

			if (isApiSuccess(response)) {
				const { accessToken, refreshToken, user } = extractTokens(response);

				if (
					accessToken &&
					refreshToken &&
					(await authenticateUser(accessToken, refreshToken, user))
				) {
					return { success: true };
				} else {
					setError('Login successful but tokens are invalid or missing.');
					return { success: false };
				}
			} else {
				const errorMessage = getApiErrorMessage(response);
				const statusCode = response.status;

				// Handle specific status-based authentication errors
				if (statusCode === 403) {
					// Check for specific account status messages
					if (errorMessage.toLowerCase().includes('pending approval')) {
						setError(
							'Your account is pending approval. Please wait for an administrator to activate your account.',
						);
					} else if (errorMessage.toLowerCase().includes('rejected')) {
						setError(
							'Your account has been rejected. Please contact support for assistance.',
						);
					} else {
						setError(errorMessage);
					}
				} else {
					setError(errorMessage);
				}

				return { success: false };
			}
		} catch (error) {
			console.error('Login failed:', error);
			setError('An unexpected error occurred. Please try again.');
			return { success: false };
		} finally {
			setIsLoading(false);
		}
	};

	const register = async (credentials: RegisterCredentials) => {
		setIsLoading(true);
		setError(null);

		try {
			if (credentials.invitationToken) {
				console.log(
					'Processing invitation acceptance with credentials:',
					credentials,
				);
				const InvitationApi = (await import('@/api/InvitationApi')).default;
				const invitationApi = new InvitationApi();

				console.log('Calling acceptInvitation API...');
				const response = await invitationApi.acceptInvitation({
					token: credentials.invitationToken,
					email: credentials.email,
					password: credentials.password,
					fullName: credentials.fullName,
				});

				console.log('Invitation acceptance response:', response);

				if (isApiSuccess(response)) {
					console.log(
						'Invitation accepted successfully, attempting auto-login...',
					);
					try {
						const authApi = new AuthAPI();
						const loginResponse = await authApi.login({
							email: credentials.email,
							password: credentials.password,
						});

						console.log('Auto-login response:', loginResponse);

						if (isApiSuccess(loginResponse)) {
							const { accessToken, refreshToken, user } =
								extractTokens(loginResponse);

							console.log('Extracted tokens and user data:', {
								accessToken: !!accessToken,
								refreshToken: !!refreshToken,
								user,
							});

							if (!accessToken || !refreshToken) {
								setError('Login successful but tokens are invalid or missing.');
								return { success: false };
							}

							const authResult = await authenticateUser(
								accessToken,
								refreshToken,
								user,
							);
							console.log('Authentication result:', authResult);

							if (authResult) {
								return { success: true };
							} else {
								setError(
									'Authentication failed after invitation acceptance. Please try signing in manually.',
								);
								return { success: false };
							}
						} else {
							setError(
								'Auto-login failed after invitation acceptance. Please sign in manually.',
							);
							return { success: false };
						}
					} catch (loginError) {
						console.error(
							'Auto-login after invitation acceptance failed:',
							loginError,
						);
						setError(
							'Invitation accepted successfully, but auto-login failed. Please sign in manually.',
						);
						return { success: false };
					}
				} else {
					const errorMessage = getApiErrorMessage(response);
					setError(errorMessage);
					return { success: false };
				}
			} else {
				const authApi = new AuthAPI();
				const response = await authApi.register(credentials);

				if (isApiSuccess(response)) {
					try {
						const loginResponse = await authApi.login({
							email: credentials.email,
							password: credentials.password,
						});

						if (isApiSuccess(loginResponse)) {
							const { accessToken, refreshToken, user } =
								extractTokens(loginResponse);

							if (
								accessToken &&
								refreshToken &&
								(await authenticateUser(accessToken, refreshToken, user))
							) {
								return { success: true };
							}
						}
					} catch (loginError) {
						console.error('Auto-login failed:', loginError);
						setError(
							'Registration successful, but auto-login failed. Please sign in manually.',
						);
						return { success: false };
					}

					router.push(
						'/signin?message=Registration successful. Please sign in.',
					);
					return { success: true, requiresSignin: true };
				} else {
					const errorMessage = getApiErrorMessage(response);
					setError(errorMessage);
					return { success: false };
				}
			}
		} catch (error) {
			console.error('Registration failed:', error);
			setError('An unexpected error occurred. Please try again.');
			return { success: false };
		} finally {
			setIsLoading(false);
		}
	};

	const logout = async () => {
		setIsLoading(true);
		try {
			await authLogout();
		} catch (error) {
			console.error('Logout failed:', error);
		} finally {
			setIsLoading(false);
		}
	};

	return {
		isLoading,
		error,
		clearError,
		login,
		register,
		logout,
	};
};
