import { useState, useCallback } from 'react';
import { useRouter } from 'next/router';
import { useGenerations } from './useGeneration';
import { SavePromptDto } from '@/api/PromptApi';
import { useAuthStore } from '@/providers/auth-store-provider';
import { GenerationResponse } from '@/api/GenerationApi';

interface UsePromptReturn {
	isLoading: boolean;
	error: string | null;
	savePrompt: (
		promptData: Omit<SavePromptDto, 'createdById'>,
	) => Promise<GenerationResponse | null>;
	clearError: () => void;
}

export const usePrompt = (): UsePromptReturn => {
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const { user } = useAuthStore((state) => state);
	const router = useRouter();
	const { createGeneration } = useGenerations();

	const savePrompt = useCallback(
		async (
			promptData: Omit<SavePromptDto, 'createdById'>,
		): Promise<GenerationResponse | null> => {
			if (!user?.id) {
				setError('User not authenticated');
				return null;
			}

			if (!promptData.prompt.trim()) {
				setError('Please enter a prompt');
				return null;
			}

			if (!promptData.projectId) {
				setError('Please select a project');
				return null;
			}

			setIsLoading(true);
			setError(null);

			try {
				const generation = await createGeneration({
					name: promptData.description || promptData.prompt.substring(0, 50),
					type: promptData.type,
					initialPrompt: promptData.prompt,
					projectId: promptData.projectId,
				});

				if (generation) {
					router.push(`/generations/${generation.id}`);
					return generation;
				} else {
					setError('Failed to create generation.');
					return null;
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to create generation';
				setError(errorMessage);
				return null;
			} finally {
				setIsLoading(false);
			}
		},
		[user?.id, router, createGeneration],
	);

	const clearError = useCallback(() => {
		setError(null);
	}, []);

	return {
		isLoading,
		error,
		savePrompt,
		clearError,
	};
};
