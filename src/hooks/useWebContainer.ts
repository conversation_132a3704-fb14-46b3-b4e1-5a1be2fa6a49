import { useEffect, useRef, useState, useCallback } from 'react';
import type { WebContainer } from '@webcontainer/api';
import { WebContainerSingleton } from '@/utils/webcontainer-singleton';

export interface FileSystemTree {
	[name: string]: {
		file?: {
			contents: string;
		};
		directory?: FileSystemTree;
	};
}

export interface UseWebContainerReturn {
	webcontainer: WebContainer | null;
	isBooting: boolean;
	isReady: boolean;
	error: string | null;
	url: string | null;
	mountFiles: (files: FileSystemTree) => Promise<void>;
	writeFile: (path: string, contents: string) => Promise<void>;
	installDependencies: () => Promise<void>;
	startDevServer: () => Promise<void>;
	stopDevServer: () => void;
	isInstalling: boolean;
	isStarting: boolean;
}

export const useWebContainer = (
	enabled: boolean = true,
): UseWebContainerReturn => {
	const [webcontainer, setWebcontainer] = useState<WebContainer | null>(null);
	const [isBooting, setIsBooting] = useState(false);
	const [isReady, setIsReady] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [url, setUrl] = useState<string | null>(null);
	const [isInstalling, setIsInstalling] = useState(false);
	const [isStarting, setIsStarting] = useState(false);

	const devServerProcessRef = useRef<{ kill: () => void } | null>(null);
	const isBootingRef = useRef(false);

	// Boot WebContainer instance (only once per session and if enabled)
	useEffect(() => {
		if (!enabled) {
			console.log('🚫 WebContainer disabled, skipping boot');
			return;
		}

		const bootWebContainer = async () => {
			// Prevent multiple boot attempts
			if (isBootingRef.current) {
				console.log('⏳ WebContainer already booting, waiting...');
				return;
			}

			// Check if we already have a WebContainer instance
			if (webcontainer) {
				console.log('✅ WebContainer already available');
				return;
			}

			try {
				setIsBooting(true);
				setError(null);
				isBootingRef.current = true;

				// Use singleton to get or create WebContainer instance
				const instance = await WebContainerSingleton.getInstance();

				setWebcontainer(instance);
				setIsReady(true);

				// Listen for server-ready event
				instance.on('server-ready', (_port: number, url: string) => {
					setUrl(url);
				});

				console.log('✅ WebContainer ready');
			} catch (err) {
				console.error('❌ WebContainer boot failed:', err);
				setError(
					err instanceof Error ? err.message : 'Failed to boot WebContainer',
				);
			} finally {
				setIsBooting(false);
				isBootingRef.current = false;
			}
		};

		bootWebContainer();
	}, [enabled]);

	// Mount files to the WebContainer file system
	const mountFiles = useCallback(
		async (files: FileSystemTree) => {
			if (!webcontainer) {
				throw new Error('WebContainer not ready');
			}

			try {
				await webcontainer.mount(
					files as import('@webcontainer/api').FileSystemTree,
				);
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to mount files';
				setError(errorMessage);
				throw new Error(errorMessage);
			}
		},
		[webcontainer],
	);

	// Write a single file
	const writeFile = useCallback(
		async (path: string, contents: string) => {
			if (!webcontainer) {
				throw new Error('WebContainer not ready');
			}

			try {
				await webcontainer.fs.writeFile(path, contents);
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to write file';
				setError(errorMessage);
				throw new Error(errorMessage);
			}
		},
		[webcontainer],
	);

	// Install dependencies
	const installDependencies = useCallback(async () => {
		if (!webcontainer) {
			throw new Error('WebContainer not ready');
		}

		try {
			setIsInstalling(true);
			setError(null);

			const installProcess = await webcontainer.spawn('npm', ['install']);
			const exitCode = await installProcess.exit;

			if (exitCode !== 0) {
				throw new Error('npm install failed');
			}
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : 'Failed to install dependencies';
			setError(errorMessage);
			throw new Error(errorMessage);
		} finally {
			setIsInstalling(false);
		}
	}, [webcontainer]);

	// Start development server
	const startDevServer = useCallback(async () => {
		if (!webcontainer) {
			throw new Error('WebContainer not ready');
		}

		try {
			setIsStarting(true);
			setError(null);

			// Stop existing dev server if running
			if (devServerProcessRef.current) {
				devServerProcessRef.current.kill();
			}

			// Start new dev server
			const devProcess = await webcontainer.spawn('npm', ['run', 'dev']);
			devServerProcessRef.current = devProcess;
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : 'Failed to start dev server';
			setError(errorMessage);
			throw new Error(errorMessage);
		} finally {
			setIsStarting(false);
		}
	}, [webcontainer]);

	// Stop development server
	const stopDevServer = useCallback(() => {
		if (devServerProcessRef.current) {
			devServerProcessRef.current.kill();
			devServerProcessRef.current = null;
			setUrl(null);
		}
	}, []);

	// Cleanup on unmount
	useEffect(() => {
		return () => {
			if (devServerProcessRef.current) {
				devServerProcessRef.current.kill();
			}
		};
	}, []);

	return {
		webcontainer,
		isBooting,
		isReady,
		error,
		url,
		mountFiles,
		writeFile,
		installDependencies,
		startDevServer,
		stopDevServer,
		isInstalling,
		isStarting,
	};
};
