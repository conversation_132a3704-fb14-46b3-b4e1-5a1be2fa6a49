import { createStore } from 'zustand/vanilla';
import { persist, createJSONStorage } from 'zustand/middleware';
import * as jose from 'jose';
import { isApiSuccess, extractApiData } from '@/common/utils/apiResponse';

export interface User {
	id: string;
	fullName: string;
	email: string;
	role: 'ADMIN' | 'USER';
	status: 'ACTIVE' | 'INACTIVE' | 'REJECTED';
}

interface TokenPayload {
	email: string;
	sub: string;
	role: 'ADMIN' | 'USER';
	status: 'ACTIVE' | 'INACTIVE' | 'REJECTED';
	type: 'auth';
	exp: number;
	iat: number;
}

export type AuthState = {
	user: User | null;
	accessToken: string | null;
	refreshToken: string | null;
	isLoading: boolean;
	error: string | null;
	isHydrated: boolean;
};

export type AuthActions = {
	setTokens: (accessToken: string, refreshToken: string) => Promise<void>;
	setUser: (user: User) => void;
	logout: () => Promise<void>;
	clearError: () => void;
	setLoading: (loading: boolean) => void;
	setError: (error: string) => void;
	refreshTokens: () => Promise<boolean>;
	_setHydrated: (hydrated: boolean) => void;
	_clearAllData: () => void;
};

export type AuthStore = AuthState & AuthActions;

export const defaultInitState: AuthState = {
	user: null,
	accessToken: null,
	refreshToken: null,
	isLoading: false,
	error: null,
	isHydrated: false,
};

const decodeToken = async (
	token: string | null | undefined,
): Promise<User | null> => {
	try {
		if (!token || typeof token !== 'string' || token.trim() === '') {
			return null;
		}

		const secret = process.env.NEXT_PUBLIC_JWT_SECRET;
		if (!secret) {
			console.error('NEXT_PUBLIC_JWT_SECRET is not defined');
			return null;
		}
		const secretKey = new TextEncoder().encode(secret);

		const { payload } = await jose.jwtVerify(token, secretKey);

		if (
			typeof payload.email !== 'string' ||
			typeof payload.sub !== 'string' ||
			typeof payload.role !== 'string' ||
			typeof payload.status !== 'string' ||
			payload.type !== 'auth'
		) {
			console.error('Invalid token payload structure:', payload);
			return null;
		}

		const typedPayload = payload as unknown as TokenPayload;

		return {
			id: typedPayload.sub,
			fullName: typedPayload.email.split('@')[0],
			email: typedPayload.email,
			role: typedPayload.role,
			status: typedPayload.status,
		};
	} catch (error) {
		if (error instanceof Error) {
			if (error.name === 'JWTExpired') {
				console.log('Token has expired');
				return null;
			} else if (error.name === 'JWTInvalid') {
				console.log('Token is invalid');
				return null;
			} else {
				console.error('Failed to decode token:', error.message);
			}
		} else {
			console.error('Failed to decode token:', error);
		}
		return null;
	}
};

export const createAuthStore = (initState: AuthState = defaultInitState) => {
	return createStore<AuthStore>()(
		persist(
			(set, get) => ({
				...initState,

				setTokens: async (accessToken: string, refreshToken: string) => {
					try {
						if (!accessToken || typeof accessToken !== 'string') {
							console.error('Invalid access token:', {
								accessToken,
								type: typeof accessToken,
							});
							set({
								accessToken: null,
								refreshToken: null,
								user: null,
								error: 'Access token must be a non-empty string',
								isLoading: false,
							});
							return;
						}

						if (!refreshToken || typeof refreshToken !== 'string') {
							console.error('Invalid refresh token:', {
								refreshToken,
								type: typeof refreshToken,
							});
							set({
								accessToken: null,
								refreshToken: null,
								user: null,
								error: 'Refresh token must be a non-empty string',
								isLoading: false,
							});
							return;
						}

						const user = await decodeToken(accessToken);

						if (!user) {
							set({
								accessToken: null,
								refreshToken: null,
								user: null,
								error: 'Invalid or expired access token',
								isLoading: false,
							});
							return;
						}

						// No cookie setting needed - using localStorage only

						set({
							accessToken,
							refreshToken,
							user,
							error: null,
							isLoading: false,
						});
					} catch (error) {
						console.error('Error setting tokens:', error);
						set({
							accessToken: null,
							refreshToken: null,
							user: null,
							error:
								error instanceof Error
									? error.message
									: 'Invalid tokens provided',
							isLoading: false,
						});
					}
				},

				setUser: (user: User) => {
					set({ user, error: null, isLoading: false });
				},

				logout: async () => {
					set({ isLoading: true });

					try {
						const { user } = get();
						if (user?.id) {
							const { AuthAPI } = await import('@/api/AuthApi');
							const authApi = new AuthAPI();
							await authApi.logout(user.id);
						}
					} catch (error) {
						console.error('Logout API call failed:', error);
					}

					set({
						user: null,
						accessToken: null,
						refreshToken: null,
						isLoading: false,
						error: null,
					});

					// No cookie removal needed - using localStorage only

					if (typeof window !== 'undefined') {
						window.location.href = '/signin';
					}
				},

				clearError: () => set({ error: null }),

				setLoading: (loading: boolean) => set({ isLoading: loading }),

				setError: (error: string) => set({ error, isLoading: false }),

				refreshTokens: async (): Promise<boolean> => {
					const { user } = get();
					if (!user?.id) return false;

					try {
						const { AuthAPI } = await import('@/api/AuthApi');
						const authApi = new AuthAPI();
						const response = await authApi.refresh(user.id);

						if (isApiSuccess(response)) {
							const tokenData = extractApiData<{ value?: string }>(response);
							const newAccessToken = tokenData?.value;

							if (newAccessToken && typeof newAccessToken === 'string') {
								const { refreshToken } = get();
								await get().setTokens(newAccessToken, refreshToken || '');
								return true;
							} else {
								console.error(
									'Invalid token format in refresh response:',
									tokenData,
								);
								await get().logout();
								return false;
							}
						}

						await get().logout();
						return false;
					} catch (error) {
						console.error('Token refresh failed:', error);
						await get().logout();
						return false;
					}
				},

				_setHydrated: (hydrated: boolean) => set({ isHydrated: hydrated }),

				_clearAllData: () => {
					set({
						user: null,
						accessToken: null,
						refreshToken: null,
						isLoading: false,
						error: null,
					});

					// No cookie removal needed - using localStorage only

					if (typeof window !== 'undefined') {
						localStorage.removeItem('auth-storage');
					}
				},
			}),
			{
				name: 'auth-storage',
				storage: createJSONStorage(() => {
					if (typeof window !== 'undefined') {
						return localStorage;
					}
					return {
						getItem: () => null,
						setItem: () => {},
						removeItem: () => {},
					};
				}),
				partialize: (state) => ({
					accessToken: state.accessToken,
					refreshToken: state.refreshToken,
					user: state.user,
				}),
				onRehydrateStorage: () => (state) => {
					if (state && typeof window !== 'undefined') {
						state._setHydrated(true);

						if (state.accessToken) {
							(async () => {
								try {
									const user = await decodeToken(state.accessToken);
									if (!user) {
										console.log('Clearing expired/invalid tokens from storage');
										state._clearAllData();
									} else {
										state.user = user;
									}
								} catch (error) {
									console.error(
										'Clearing invalid auth data due to validation error:',
										error,
									);
									state._clearAllData();
								}
							})();
						}
					} else if (typeof window === 'undefined') {
						if (state) {
							state._setHydrated(true);
						}
					}
				},
			},
		),
	);
};
