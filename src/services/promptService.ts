import { Prompt<PERSON><PERSON>, SavePromptDto } from '@/api/PromptApi';
import {
	extractApiData,
	getApiErrorMessage,
	isApiSuccess,
} from '@/common/utils/apiResponse';

export interface SavePromptResult {
	id: string;
	prompt: string;
	type: 'DOCUMENTATION' | 'UI';
	description?: string;
	tags?: string[];
	projectId?: string;
	createdById: string;
	createdAt: string;
	updatedAt: string;
}

/**
 * Client-side service for saving prompts
 * This runs in the browser
 */
export async function savePromptClientSide(
	promptData: SavePromptDto
): Promise<{ prompt?: SavePromptResult; error?: string }> {
	try {
		const api = new PromptAPI();
		const response = await api.savePrompt(promptData);

		if (isApiSuccess(response)) {
			const prompt = extractApiData<SavePromptResult>(response);
			if (prompt) {
				return { prompt };
			} else {
				return { error: 'Failed to save prompt: Invalid response data' };
			}
		} else {
			const errorMessage = getApiErrorMessage(response);
			return { error: errorMessage };
		}
	} catch (error) {
		console.error('Error saving prompt:', error);
		return {
			error:
				error instanceof Error
					? error.message
					: 'Network error or server unavailable',
		};
	}
}
