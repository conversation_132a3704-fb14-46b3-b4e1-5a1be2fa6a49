import { StandardApiResponse } from '@/common/StandardApi/types';

/**
 * Utility functions for handling standardized API responses
 */

/**
 * Type guard to check if response data follows the standard API response structure
 */
export function isStandardApiResponse(
	data: unknown,
): data is StandardApiResponse {
	return (
		typeof data === 'object' &&
		data !== null &&
		'message' in data &&
		'statusCode' in data &&
		typeof (data as StandardApiResponse).message === 'string' &&
		typeof (data as StandardApiResponse).statusCode === 'number'
	);
}

/**
 * Extract data from a standardized API response
 */
export function extractApiData<T = unknown>(response: {
	data: unknown;
}): T | null {
	if (isStandardApiResponse(response.data)) {
		return (response.data.data as T) || null;
	}
	return null;
}

/**
 * Extract message from a standardized API response
 */
export function extractApiMessage(response: { data: unknown }): string {
	if (isStandardApiResponse(response.data)) {
		return response.data.message;
	}
	return 'Unknown error occurred';
}

/**
 * Extract status code from a standardized API response
 */
export function extractApiStatusCode(response: { data: unknown }): number {
	if (isStandardApiResponse(response.data)) {
		return response.data.statusCode;
	}
	return 500;
}

/**
 * Check if API response indicates success
 */
export function isApiSuccess(response: {
	status: number;
	data: unknown;
}): boolean {
	return (
		response.status >= 200 &&
		response.status < 300 &&
		isStandardApiResponse(response.data) &&
		response.data.statusCode >= 200 &&
		response.data.statusCode < 300
	);
}

/**
 * Check if API response indicates an error
 */
export function isApiError(response: {
	status: number;
	data: unknown;
}): boolean {
	return !isApiSuccess(response);
}

/**
 * Get error message from API response
 */
export function getApiErrorMessage(response: {
	status: number;
	data: unknown;
}): string {
	if (isStandardApiResponse(response.data)) {
		return response.data.message;
	}

	const data = response.data as { message?: string; error?: string };
	return (
		data?.message ||
		data?.error ||
		`Request failed with status ${response.status}`
	);
}

/**
 * Type-safe wrapper for API responses with authentication data
 */
export interface AuthResponseData {
	accessToken: string;
	refreshToken: string;
	user?: {
		id: string;
		fullName?: string;
		email: string;
		role: 'ADMIN' | 'USER';
		status: 'ACTIVE' | 'INACTIVE' | 'REJECTED';
	};
}

/**
 * Extract authentication tokens from API response
 */
export function extractAuthTokens(response: { data: unknown }): {
	accessToken?: string;
	refreshToken?: string;
	user?: AuthResponseData['user'];
} {
	const authData = extractApiData<AuthResponseData>(response);

	if (authData) {
		return {
			accessToken: authData.accessToken,
			refreshToken: authData.refreshToken,
			user: authData.user,
		};
	}

	return {};
}

/**
 * Type-safe wrapper for invitation data
 */
export interface InvitationResponseData {
	email: string;
	status: string;
	token?: string;
}

/**
 * Extract invitation data from API response
 */
export function extractInvitationData(response: {
	data: unknown;
}): InvitationResponseData | null {
	return extractApiData<InvitationResponseData>(response);
}

/**
 * Create a standardized error response for client-side errors
 */
export function createClientErrorResponse(
	message: string,
	statusCode: number = 400,
): StandardApiResponse {
	return {
		message,
		statusCode,
	};
}

/**
 * Handle API response with proper error handling and type safety
 */
export function handleApiResponse<T = unknown>(
	response: { status: number; data: unknown },
	onSuccess: (data: T, message: string) => void,
	onError: (message: string, statusCode: number) => void,
): void {
	if (isApiSuccess(response)) {
		const data = extractApiData<T>(response);
		const message = extractApiMessage(response);
		onSuccess(data as T, message);
	} else {
		const message = getApiErrorMessage(response);
		const statusCode = extractApiStatusCode(response);
		onError(message, statusCode);
	}
}
