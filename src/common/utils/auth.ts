
/**
 * Clear all authentication data from browser storage
 * Useful for debugging or manual cleanup
 */
export const clearAllAuthData = () => {
	if (typeof window !== 'undefined') {
		localStorage.removeItem('auth-storage');
		
		document.cookie = 'auth-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
		
		console.log('All authentication data cleared');
	}
};

/**
 * Check if there's any auth data in storage
 */
export const hasAuthData = (): boolean => {
	if (typeof window === 'undefined') return false;
	
	const hasLocalStorage = localStorage.getItem('auth-storage') !== null;
	const hasCookie = document.cookie.includes('auth-token=');
	
	return hasLocalStorage || hasCookie;
};
