import { Httpstatus } from '../interface';

export type HeaderObject = {
	Authorization: string;
	'Content-Type': string;
	accept: string;
	[key: string]: string;
};
export interface Refresh<PERSON>son extends Partial<Response> {
	accessToken: string;
	refreshtoken: string;
}
export interface StandardApiResponse<T = unknown> {
	message: string;
	statusCode: number;
	data?: T;
}

export interface ApiResponse {
	status: Httpstatus;
	data: unknown; 
}
