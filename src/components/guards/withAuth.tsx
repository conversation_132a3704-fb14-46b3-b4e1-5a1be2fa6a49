import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { NextPageWithLayout } from '@/pages/_app';
import { Typography } from '@/components/atoms';
import { useAuthStore } from '@/providers/auth-store-provider';
import { useAuth } from '@/hooks/useAuth';

interface AuthGuardProps {
	children: React.ReactNode;
	fallback?: React.ReactNode;
}

interface WithAuthOptions {
	redirectTo?: string;
	fallback?: React.ReactNode;
}

/**
 * Higher-order component that protects pages requiring authentication
 * Uses Zustand store to check authentication state
 */
export function withAuth<P extends object>(
	WrappedComponent: NextPageWithLayout<P>,
	options: WithAuthOptions = {},
) {
	const { redirectTo = '/signin', fallback } = options;

	const AuthenticatedComponent: NextPageWithLayout<P> = (props) => {
		const router = useRouter();
		const user = useAuthStore((state) => state.user);
		const isHydrated = useAuthStore((state) => state.isHydrated);
		const { logout } = useAuth();
		const [isChecking, setIsChecking] = useState(true);

		useEffect(() => {
			if (!isHydrated) {
				// Wait for store to hydrate
				return;
			}

			if (!user) {
				// User is not authenticated, redirect to signin
				router.push(redirectTo);
				return;
			}

			// Check if user has valid status for accessing protected content
			if (user.status === 'INACTIVE' || user.status === 'REJECTED') {
				// Log out users with invalid status and redirect to signin
				logout();
				router.push('/signin');
				return;
			}

			// User is authenticated and has valid status
			setIsChecking(false);
		}, [user, isHydrated, router, logout]);

		// Show loading state while checking authentication or store is not hydrated
		if (!isHydrated || isChecking) {
			return (
				fallback || (
					<div className='min-h-screen flex items-center justify-center bg-base'>
						<div className='text-center'>
							<div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4'></div>
							<Typography
								variant='body'
								className='text-muted'>
								Loading...
							</Typography>
						</div>
					</div>
				)
			);
		}

		// If user is not authenticated, don't render anything (redirect will happen)
		if (!user) {
			return null;
		}

		return <WrappedComponent {...props} />;
	};

	// Copy static properties
	if (WrappedComponent.getLayout) {
		AuthenticatedComponent.getLayout = WrappedComponent.getLayout;
	}

	AuthenticatedComponent.displayName = `withAuth(${
		WrappedComponent.displayName || WrappedComponent.name || 'Component'
	})`;

	return AuthenticatedComponent;
}

/**
 * Component-level auth guard for protecting specific components
 */
export function AuthGuard({ children, fallback }: AuthGuardProps) {
	const user = useAuthStore((state) => state.user);
	const isHydrated = useAuthStore((state) => state.isHydrated);
	const { logout } = useAuth();
	const router = useRouter();

	useEffect(() => {
		if (
			isHydrated &&
			user &&
			(user.status === 'INACTIVE' || user.status === 'REJECTED')
		) {
			// Log out users with invalid status
			logout();
			router.push('/signin');
		}
	}, [user, isHydrated, logout, router]);

	// Show loading state while store is hydrating
	if (!isHydrated) {
		return (
			fallback || (
				<div className='flex items-center justify-center p-8'>
					<div className='text-center'>
						<div className='animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2'></div>
						<Typography
							variant='body-sm'
							className='text-muted'>
							Loading...
						</Typography>
					</div>
				</div>
			)
		);
	}

	// If user is not authenticated or has invalid status, don't render children
	if (!user || user.status === 'INACTIVE' || user.status === 'REJECTED') {
		return (
			fallback || (
				<div className='flex items-center justify-center p-8'>
					<div className='text-center'>
						<Typography
							variant='body-sm'
							className='text-muted'>
							Please sign in to access this content.
						</Typography>
					</div>
				</div>
			)
		);
	}

	return <>{children}</>;
}

export default withAuth;
