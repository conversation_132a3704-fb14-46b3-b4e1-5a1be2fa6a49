interface DividerProps extends React.HTMLAttributes<HTMLHRElement> {
	className?: string;
	variant?: 'default' | 'muted';
}

const Divider: React.FC<DividerProps> = ({
	className,
	variant = 'default',
	...props
}) => {
	const variantStyles = {
		default: 'border-border-secondary',
		muted: 'border-border-muted',
	};

	return (
		<hr
			className={`border-t ${variantStyles[variant]} my-4 ${className}`}
			{...props}
		/>
	);
};

Divider.displayName = 'Divider';
export default Divider;
