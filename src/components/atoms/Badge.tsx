import { forwardRef } from 'react';
import { clsx } from 'clsx';

interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
	className?: string;
	children: React.ReactNode;
	variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
	size?: 'small' | 'medium' | 'large';
	emphasis?: 'light' | 'filled' | 'outline';
	icon?: React.ReactNode;
	removable?: boolean;
	onRemove?: () => void;
}

const badgeVariants = {
	primary: {
		light: ['text-primary-700', 'bg-primary/15', 'border-primary/30'],
		filled: ['text-white', 'bg-primary', 'border-primary'],
		outline: ['text-primary-600', 'bg-transparent', 'border-primary'],
	},
	secondary: {
		light: ['text-muted-foreground', 'bg-secondary/20', 'border-border'],
		filled: ['text-foreground', 'bg-secondary', 'border-secondary'],
		outline: ['text-muted-foreground', 'bg-transparent', 'border-border'],
	},
	success: {
		light: ['text-success-700', 'bg-success/15', 'border-success/30'],
		filled: ['text-white', 'bg-success', 'border-success'],
		outline: ['text-success-600', 'bg-transparent', 'border-success'],
	},
	warning: {
		light: ['text-warning-700', 'bg-warning/15', 'border-warning/30'],
		filled: ['text-gray-900', 'bg-warning', 'border-warning'],
		outline: ['text-warning-600', 'bg-transparent', 'border-warning'],
	},
	error: {
		light: ['text-danger-700', 'bg-danger/15', 'border-danger/30'],
		filled: ['text-white', 'bg-danger', 'border-danger'],
		outline: ['text-danger-600', 'bg-transparent', 'border-danger'],
	},
	info: {
		light: ['text-info-700', 'bg-info/15', 'border-info/30'],
		filled: ['text-white', 'bg-info', 'border-info'],
		outline: ['text-info-600', 'bg-transparent', 'border-info'],
	},
};

const badgeSizes = {
	small: {
		base: 'px-2 p-0.5 text-xs',
		icon: 'w-3 h-3',
		remove: 'w-3 h-3 ml-0.5',
	},
	medium: {
		base: 'px-1.5 py-0.5 text-sm',
		icon: 'w-3.5 h-3.5',
		remove: 'w-3.5 h-3.5 ml-1',
	},
	large: {
		base: 'px-2 py-1 text-base',
		icon: 'w-4 h-4',
		remove: 'w-4 h-4 ml-1.5',
	},
};

const Badge = forwardRef<HTMLSpanElement, BadgeProps>(
	(
		{
			className,
			children,
			variant = 'primary',
			size = 'medium',
			emphasis = 'light',
			icon,
			removable = false,
			onRemove,
			...props
		},
		ref,
	) => {
		const handleRemove = (e: React.MouseEvent) => {
			e.stopPropagation();
			onRemove?.();
		};

		return (
			<span
				ref={ref}
				className={clsx(
					'inline-flex items-center justify-center',
					'font-medium uppercase rounded-md',
					'transition-all duration-200 ease-in-out',
					'whitespace-nowrap',
					badgeSizes[size].base,
					badgeVariants[variant][emphasis],
					className,
				)}
				{...props}>
				{icon && (
					<span className={clsx('flex-shrink-0', badgeSizes[size].icon)}>
						{icon}
					</span>
				)}

				<span className={clsx(icon && 'ml-1')}>{children}</span>

				{removable && (
					<button
						type='button'
						onClick={handleRemove}
						className={clsx(
							'flex-shrink-0 rounded',
							'hover:bg-muted/10',
							'focus:outline-none focus:ring-1 focus:ring-current',
							'transition-colors duration-150',
							badgeSizes[size].remove,
						)}
						aria-label='Remove badge'>
						<svg
							className='w-full h-full'
							fill='none'
							stroke='currentColor'
							viewBox='0 0 24 24'>
							<path
								strokeLinecap='round'
								strokeLinejoin='round'
								strokeWidth={2}
								d='M6 18L18 6M6 6l12 12'
							/>
						</svg>
					</button>
				)}
			</span>
		);
	},
);

Badge.displayName = 'Badge';

export default Badge;
export type { BadgeProps };
