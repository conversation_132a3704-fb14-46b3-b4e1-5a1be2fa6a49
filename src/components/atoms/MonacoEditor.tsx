import { useRef, useEffect, useState, useCallback } from 'react';
import Editor, { OnMount, OnChange } from '@monaco-editor/react';
import * as monaco from 'monaco-editor';
import { stripMarkdownCodeBlocks } from '@/utils/markdown';

interface MonacoEditorProps {
	value?: string;
	language?: string;
	theme?: 'vs-dark' | 'light' | 'vs';
	height?: string | number;
	width?: string | number;
	readOnly?: boolean;
	onChange?: (value: string | undefined) => void;
	onMount?: (editor: monaco.editor.IStandaloneCodeEditor) => void;
	options?: monaco.editor.IStandaloneEditorConstructionOptions;
	className?: string;
	// Streaming specific props
	streamingValue?: string;
	enableStreaming?: boolean;
	streamingDelay?: number;
}

export const MonacoEditor: React.FC<MonacoEditorProps> = ({
	value = '',
	language = 'typescript',
	theme = 'vs-dark',
	height = '100%',
	width = '100%',
	readOnly = false,
	onChange,
	onMount,
	options = {},
	className = '',
	streamingValue,
	enableStreaming = false,
	streamingDelay = 50,
}) => {
	const editorRef = useRef<monaco.editor.IStandaloneCodeEditor | null>(null);
	const [currentValue, setCurrentValue] = useState(value);
	const [isStreaming, setIsStreaming] = useState(false);
	const streamingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
	const streamingIndexRef = useRef(0);
	const isMountedRef = useRef(true);

	// Default editor options optimized for code generation
	const defaultOptions: monaco.editor.IStandaloneEditorConstructionOptions = {
		minimap: { enabled: true },
		fontSize: 14,
		lineHeight: 20,
		fontFamily:
			'"Fira Code", "Cascadia Code", "JetBrains Mono", Consolas, "Courier New", monospace',
		fontLigatures: true,
		wordWrap: 'on',
		automaticLayout: true,
		scrollBeyondLastLine: false,
		renderLineHighlight: 'line',
		selectOnLineNumbers: true,
		roundedSelection: false,
		readOnly,
		cursorStyle: 'line',
		mouseWheelZoom: true,
		smoothScrolling: true,
		cursorBlinking: 'blink',
		cursorSmoothCaretAnimation: 'on',
		renderWhitespace: 'selection',
		bracketPairColorization: { enabled: true },
		guides: {
			bracketPairs: true,
			indentation: true,
		},
		suggest: {
			showKeywords: true,
			showSnippets: true,
		},
		...options,
	};

	// Handle editor mount
	const handleEditorMount: OnMount = useCallback(
		(editor, monaco) => {
			editorRef.current = editor;

			// Configure Monaco for better TypeScript and TSX support
			const compilerOptions = {
				target: monaco.languages.typescript.ScriptTarget.Latest,
				allowNonTsExtensions: true,
				moduleResolution:
					monaco.languages.typescript.ModuleResolutionKind.NodeJs,
				module: monaco.languages.typescript.ModuleKind.ESNext,
				noEmit: true,
				esModuleInterop: true,
				allowSyntheticDefaultImports: true,
				jsx: monaco.languages.typescript.JsxEmit.ReactJSX,
				jsxImportSource: 'react',
				allowJs: true,
				strict: false, // More lenient for generated code
				skipLibCheck: true,
				typeRoots: ['node_modules/@types'],
			};

			// Apply to both TypeScript and TypeScript React
			monaco.languages.typescript.typescriptDefaults.setCompilerOptions(
				compilerOptions,
			);
			monaco.languages.typescript.typescriptDefaults.setDiagnosticsOptions({
				noSemanticValidation: false,
				noSyntaxValidation: false,
				noSuggestionDiagnostics: true, // Reduce noise in generated code
			});

			// Add comprehensive React types for better IntelliSense
			monaco.languages.typescript.typescriptDefaults.addExtraLib(
				`
				declare module 'react' {
					export = React;
					export as namespace React;
					namespace React {
						// Basic React types
						interface Component<P = {}, S = {}> {
							props: Readonly<P>;
							state: Readonly<S>;
							render(): ReactNode;
						}

						interface FunctionComponent<P = {}> {
							(props: P): JSX.Element | null;
							displayName?: string;
						}

						type FC<P = {}> = FunctionComponent<P>;
						type ReactNode = JSX.Element | string | number | null | undefined;

						// Common hooks
						function useState<S>(initialState: S | (() => S)): [S, (value: S | ((prev: S) => S)) => void];
						function useEffect(effect: () => void | (() => void), deps?: any[]): void;
						function useCallback<T extends (...args: any[]) => any>(callback: T, deps: any[]): T;
						function useMemo<T>(factory: () => T, deps: any[]): T;
						function useRef<T>(initialValue: T): { current: T };

						// Event types
						interface MouseEvent<T = Element> {
							preventDefault(): void;
							stopPropagation(): void;
							target: T;
						}

						interface ChangeEvent<T = Element> {
							target: T & { value: string };
						}

						interface KeyboardEvent<T = Element> {
							key: string;
							preventDefault(): void;
							stopPropagation(): void;
						}
					}
				}

				// JSX namespace for TSX support
				declare namespace JSX {
					interface Element {}
					interface IntrinsicElements {
						div: any;
						span: any;
						button: any;
						input: any;
						textarea: any;
						form: any;
						h1: any;
						h2: any;
						h3: any;
						h4: any;
						h5: any;
						h6: any;
						p: any;
						a: any;
						img: any;
						ul: any;
						ol: any;
						li: any;
						nav: any;
						header: any;
						footer: any;
						main: any;
						section: any;
						article: any;
						aside: any;
					}
				}
				`,
				'file:///node_modules/@types/react/index.d.ts',
			);

			// Call custom onMount if provided
			if (onMount) {
				onMount(editor);
			}
		},
		[onMount],
	);

	// Handle editor value changes
	const handleEditorChange: OnChange = useCallback(
		(value) => {
			if (!isStreaming) {
				setCurrentValue(value || '');
				if (onChange) {
					onChange(value);
				}
			}
		},
		[onChange, isStreaming],
	);

	// Streaming effect
	useEffect(() => {
		if (!enableStreaming || !streamingValue || !editorRef.current) {
			return;
		}

		// Strip markdown code blocks from the streaming value
		const cleanStreamingValue = stripMarkdownCodeBlocks(streamingValue);

		// Reset streaming state
		setIsStreaming(true);
		streamingIndexRef.current = 0;

		const streamText = () => {
			// Check if component is still mounted and editor exists
			if (!isMountedRef.current || !editorRef.current || !cleanStreamingValue) {
				setIsStreaming(false);
				return;
			}

			const currentIndex = streamingIndexRef.current;
			if (currentIndex >= cleanStreamingValue.length) {
				setIsStreaming(false);
				setCurrentValue(cleanStreamingValue);
				return;
			}

			// Get the next chunk of text to display
			const nextIndex = Math.min(currentIndex + 1, cleanStreamingValue.length);
			const newValue = cleanStreamingValue.substring(0, nextIndex);

			try {
				// Update editor value with error handling
				if (editorRef.current && isMountedRef.current) {
					editorRef.current.setValue(newValue);
					setCurrentValue(newValue);

					// Move cursor to end
					const model = editorRef.current.getModel();
					if (model) {
						const lineCount = model.getLineCount();
						const lastLineLength = model.getLineLength(lineCount);
						editorRef.current.setPosition({
							lineNumber: lineCount,
							column: lastLineLength + 1,
						});
					}
				}
			} catch (error) {
				// Handle errors during editor update (like disposed model)
				console.log('Monaco editor update error (non-critical):', error);
				// Stop streaming if we encounter an error
				if (streamingTimeoutRef.current) {
					clearTimeout(streamingTimeoutRef.current);
				}
				setIsStreaming(false);
				return;
			}

			streamingIndexRef.current = nextIndex;

			// Schedule next update
			streamingTimeoutRef.current = setTimeout(streamText, streamingDelay);
		};

		// Start streaming
		streamText();

		// Cleanup
		return () => {
			if (streamingTimeoutRef.current) {
				clearTimeout(streamingTimeoutRef.current);
			}
			setIsStreaming(false);
		};
	}, [streamingValue, enableStreaming, streamingDelay]);

	// Update current value when value prop changes (non-streaming)
	useEffect(() => {
		if (!enableStreaming && value !== currentValue && isMountedRef.current) {
			try {
				const cleanValue = stripMarkdownCodeBlocks(value);
				setCurrentValue(cleanValue);
			} catch (error) {
				console.log('Error updating Monaco editor value:', error);
			}
		}
	}, [value, enableStreaming, currentValue]);

	// Cleanup on unmount
	useEffect(() => {
		return () => {
			// Mark component as unmounted to prevent further updates
			isMountedRef.current = false;

			// Clear any pending timeouts
			if (streamingTimeoutRef.current) {
				clearTimeout(streamingTimeoutRef.current);
			}
		};
	}, []);

	return (
		<div className={`monaco-editor-container ${className}`}>
			<Editor
				height={height}
				width={width}
				language={language}
				theme={theme}
				value={currentValue}
				options={defaultOptions}
				onMount={handleEditorMount}
				onChange={handleEditorChange}
				loading={
					<div className='flex items-center justify-center h-full'>
						<div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary'></div>
					</div>
				}
			/>
		</div>
	);
};

export default MonacoEditor;
