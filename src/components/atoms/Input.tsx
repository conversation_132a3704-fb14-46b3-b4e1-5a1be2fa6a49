import { Input as HeadlessInput } from '@headlessui/react';
import { forwardRef } from 'react';
import { clsx } from 'clsx';

interface InputProps
	extends React.ComponentProps<typeof HeadlessInput>,
		Omit<
			React.InputHTMLAttributes<HTMLInputElement>,
			keyof React.ComponentProps<typeof HeadlessInput> | 'size'
		> {
	className?: string;
	inputSize?: 'small' | 'medium' | 'large';
	variant?: 'default' | 'success' | 'danger';
}

const inputSizes = {
	small: 'px-2 py-1 text-sm h-8',
	medium: 'px-3 py-2 text-base h-10',
	large: 'px-4 py-3 text-lg h-12',
};

const inputVariants = {
	default: [
		'bg-surface text-foreground',
		'border border-border-secondary',
		'focus:border-primary focus:ring-1 focus:ring-primary',
		'hover:border-border',
	],
	success: [
		'bg-surface text-foreground',
		'border border-success-600',
		'focus:border-success focus:ring-1 focus:ring-success',
	],
	danger: [
		'bg-surface text-foreground',
		'border border-danger-600',
		'focus:border-danger focus:ring-1 focus:ring-danger',
	],
};

const Input = forwardRef<HTMLInputElement, InputProps>(
	({ className, inputSize = 'medium', variant = 'default', ...props }, ref) => {
		return (
			<HeadlessInput
				ref={ref}
				className={clsx(
					'block w-full rounded-md',
					'transition-all duration-200 ease-in-out',
					'focus:outline-none',
					'placeholder:text-muted',
					'disabled:cursor-not-allowed disabled:opacity-50',
					'disabled:bg-surface',
					inputSizes[inputSize],
					inputVariants[variant],
					className,
				)}
				{...props}
			/>
		);
	},
);

Input.displayName = 'Input';
export default Input;
