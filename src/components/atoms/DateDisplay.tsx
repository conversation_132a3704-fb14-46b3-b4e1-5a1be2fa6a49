import Typography from './Typography';
import {
	formatDate,
	formatRelativeTime,
	formatSmartDate,
	formatProjectDate,
	formatFullDate,
	isValidDate,
} from '@/common/utils/dateUtils';
import type { TypographyProps } from './Typography';

export type DateFormatType =
	| 'default'
	| 'relative'
	| 'smart'
	| 'project'
	| 'full'
	| 'custom';

interface DateDisplayProps {
	date: string | Date | null | undefined;
	format?: DateFormatType;
	customFormat?: string;
	fallback?: string;
	prefix?: string;
	suffix?: string;
	variant?: TypographyProps['variant'];
	color?: TypographyProps['color'];
	weight?: TypographyProps['weight'];
	className?: string;
	showTooltip?: boolean;
	title?: string;
}

const DateDisplay = ({
	date,
	format: formatType = 'default',
	customFormat,
	fallback = 'Not available',
	prefix,
	suffix,
	variant = 'body-sm',
	color = 'secondary',
	weight,
	className,
	showTooltip = false,
	title,
}: DateDisplayProps) => {
	if (!isValidDate(date)) {
		return (
			<Typography
				variant={variant}
				color={color}
				weight={weight}
				className={className}
				as='span'>
				{prefix}
				{fallback}
				{suffix}
			</Typography>
		);
	}

	const getFormattedDate = (): string => {
		switch (formatType) {
			case 'relative':
				return formatRelativeTime(date);
			case 'smart':
				return formatSmartDate(date);
			case 'project':
				return formatProjectDate(date);
			case 'full':
				return formatFullDate(date);
			case 'custom':
				return customFormat ? formatDate(date, customFormat) : formatDate(date);
			case 'default':
			default:
				return formatDate(date);
		}
	};

	const formattedDate = getFormattedDate();

	const tooltipText = showTooltip ? title || formatFullDate(date) : title;

	return (
		<Typography
			variant={variant}
			color={color}
			weight={weight}
			className={className}
			title={tooltipText}
			as='span'>
			{prefix}
			{formattedDate}
			{suffix}
		</Typography>
	);
};

DateDisplay.displayName = 'DateDisplay';

export default DateDisplay;
