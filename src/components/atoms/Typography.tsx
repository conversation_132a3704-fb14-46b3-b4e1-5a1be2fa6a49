import React from 'react';
import { clsx } from 'clsx';

type TypographyVariant =
	| 'h1'
	| 'h2'
	| 'h3'
	| 'h4'
	| 'h5'
	| 'h6'
	| 'body-lg'
	| 'body'
	| 'body-sm'
	| 'caption'
	| 'overline'
	| 'display-lg'
	| 'display'
	| 'display-sm';

type TypographyWeight =
	| 'light'
	| 'regular'
	| 'medium'
	| 'semibold'
	| 'bold'
	| 'extrabold';

type TypographyAlign = 'left' | 'center' | 'right' | 'justify';

type TypographyColor =
	| 'primary'
	| 'secondary'
	| 'tertiary'
	| 'disabled'
	| 'inverse'
	| 'accent'
	| 'positive'
	| 'negative'
	| 'notice'
	| 'informative';

interface TypographyProps {
	variant?: TypographyVariant;
	weight?: TypographyWeight;
	align?: TypographyAlign;
	color?: TypographyColor;
	responsive?: boolean;
	className?: string;
	children: React.ReactNode;
	as?: keyof React.JSX.IntrinsicElements;
	title?: string;
}

const variantStyles = {
	'display-lg': 'text-5xl font-bold leading-tight',
	display: 'text-4xl font-bold leading-tight',
	'display-sm': 'text-3xl font-bold leading-tight',

	h1: 'text-3xl font-bold leading-tight',
	h2: 'text-2xl font-semibold leading-tight',
	h3: 'text-xl font-semibold leading-snug',
	h4: 'text-lg font-medium leading-snug',
	h5: 'text-base font-medium leading-normal',
	h6: 'text-sm font-medium leading-normal',

	'body-lg': 'text-lg font-normal leading-relaxed',
	body: 'text-base font-normal leading-normal',
	'body-sm': 'text-sm font-normal leading-normal',

	caption: 'text-xs font-normal leading-tight',
	overline: 'text-xs font-medium uppercase tracking-wide leading-tight',
};

const nonResponsiveVariantStyles = {
	'display-lg': 'text-6xl font-bold leading-tight',
	display: 'text-5xl font-bold leading-tight',
	'display-sm': 'text-4xl font-bold leading-tight',

	h1: 'text-3xl font-bold leading-tight',
	h2: 'text-2xl font-semibold leading-tight',
	h3: 'text-xl font-semibold leading-snug',
	h4: 'text-lg font-medium leading-snug',
	h5: 'text-base font-medium leading-normal',
	h6: 'text-sm font-medium leading-normal',

	'body-lg': 'text-lg font-normal leading-relaxed',
	body: 'text-base font-normal leading-normal',
	'body-sm': 'text-sm font-normal leading-normal',

	caption: 'text-xs font-normal leading-tight',
	overline: 'text-xs font-medium uppercase tracking-wide leading-tight',
};

const weightStyles = {
	light: 'font-light',
	regular: 'font-regular',
	medium: 'font-medium',
	semibold: 'font-semibold',
	bold: 'font-bold',
	extrabold: 'font-extrabold',
};

const alignStyles = {
	left: 'text-left',
	center: 'text-center',
	right: 'text-right',
	justify: 'text-justify',
};

const colorStyles = {
	primary: 'text-foreground',
	secondary: 'text-muted',
	tertiary: 'text-muted-foreground',
	disabled: 'text-muted-foreground',
	inverse: 'text-white',
	accent: 'text-primary',
	positive: 'text-success',
	negative: 'text-danger',
	notice: 'text-warning',
	informative: 'text-info',
};

const defaultElements: Record<
	TypographyVariant,
	keyof React.JSX.IntrinsicElements
> = {
	'display-lg': 'h1',
	display: 'h1',
	'display-sm': 'h1',
	h1: 'h1',
	h2: 'h2',
	h3: 'h3',
	h4: 'h4',
	h5: 'h5',
	h6: 'h6',
	'body-lg': 'p',
	body: 'p',
	'body-sm': 'p',
	caption: 'span',
	overline: 'span',
};

const Typography: React.FC<TypographyProps> = ({
	variant = 'body',
	weight,
	align = 'left',
	color = 'primary',
	responsive = true,
	className,
	children,
	as,
	...props
}) => {
	const Element = as || defaultElements[variant];
	const variantStyleMap = responsive
		? variantStyles
		: nonResponsiveVariantStyles;

	return (
		<Element
			className={clsx(
				'transition-colors duration-200 ease-in-out',
				variantStyleMap[variant],
				weight && weightStyles[weight],
				alignStyles[align],
				colorStyles[color],
				className,
			)}
			{...props}>
			{children}
		</Element>
	);
};

Typography.displayName = 'Typography';

export default Typography;
export type {
	TypographyProps,
	TypographyVariant,
	TypographyWeight,
	TypographyAlign,
	TypographyColor,
};
