import { clsx } from 'clsx';

interface AlertProps extends React.HTMLAttributes<HTMLDivElement> {
	className?: string;
	children: React.ReactNode;
	variant?: 'default' | 'informative' | 'positive' | 'notice' | 'negative';
	size?: 'small' | 'medium' | 'large';
	icon?: React.ReactNode;
}

const alertVariants = {
	default: ['bg-surface text-foreground', 'border border-border-secondary'],
	informative: ['bg-info/15 text-info-700', 'border border-info/30'],
	positive: ['bg-success/15 text-success-700', 'border border-success/30'],
	notice: ['bg-warning/15 text-warning-700', 'border border-warning/30'],
	negative: ['bg-danger/15 text-danger-700', 'border border-danger/30'],
};

const alertSizes = {
	small: 'p-3 text-sm',
	medium: 'p-4 text-base',
	large: 'p-6 text-lg',
};

const Alert: React.FC<AlertProps> = ({
	className,
	children,
	variant = 'default',
	size = 'medium',
	icon,
	...props
}) => {
	return (
		<div
			className={clsx(
				'rounded shadow-sm',
				'transition-all duration-200 ease-in-out',
				alertVariants[variant],
				alertSizes[size],
				className,
			)}
			role='alert'
			{...props}>
			<div className='flex items-start gap-1.5'>
				{icon && <span className='flex-shrink-0 w-4 h-4 mt-0.5'>{icon}</span>}
				<div className='flex-1'>{children}</div>
			</div>
		</div>
	);
};

Alert.displayName = 'Alert';
export default Alert;
