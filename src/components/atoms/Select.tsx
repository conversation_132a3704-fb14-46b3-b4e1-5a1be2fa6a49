import { useState, Fragment } from 'react';
import {
	Combobox,
	ComboboxInput,
	ComboboxButton,
	ComboboxOptions,
	ComboboxOption,
	Transition,
} from '@headlessui/react';
import {
	ChevronUpDownIcon,
	CheckIcon,
	XMarkIcon,
} from '@heroicons/react/24/outline';
import { clsx } from 'clsx';
import Image from 'next/image';

export interface SelectOption {
	id: string | number;
	label: string;
	value: string | number;
	disabled?: boolean;
	avatar?: string;
	description?: string;
}

interface SelectProps {
	options: SelectOption[];
	value?: SelectOption | SelectOption[];
	onChange: (value: SelectOption | SelectOption[] | null) => void;
	onInputChange?: (query: string) => void;
	placeholder?: string;
	searchPlaceholder?: string;
	className?: string;
	disabled?: boolean;
	multiple?: boolean;
	clearable?: boolean;
	size?: 'sm' | 'md' | 'lg';
	variant?: 'default' | 'success' | 'danger';
	loading?: boolean;
	emptyMessage?: string;
	maxDisplayedItems?: number;
}

const selectSizes = {
	sm: 'px-2 py-1 text-sm h-8',
	md: 'px-3 py-2 text-base h-10',
	lg: 'px-4 py-3 text-lg h-12',
};

const selectVariants = {
	default: [
		'bg-surface text-foreground',
		'border border-border-secondary',
		'focus:border-primary focus:ring-1 focus:ring-primary',
		'hover:border-border',
	],
	success: [
		'bg-surface text-foreground',
		'border border-success-600',
		'focus:border-success focus:ring-1 focus:ring-success',
	],
	danger: [
		'bg-surface text-foreground',
		'border border-danger-600',
		'focus:border-danger focus:ring-1 focus:ring-danger',
	],
};

const Select: React.FC<SelectProps> = ({
	options,
	value,
	onChange,
	onInputChange,
	placeholder = 'Select an option...',
	searchPlaceholder = 'Search...',
	className,
	disabled = false,
	multiple = false,
	clearable = false,
	size = 'md',
	variant = 'default',
	loading = false,
	emptyMessage = 'No options found',
	maxDisplayedItems = 3,
}) => {
	const [query, setQuery] = useState('');

	const filteredOptions =
		query === ''
			? options
			: options.filter(
					(option) =>
						option.label.toLowerCase().includes(query.toLowerCase()) ||
						option.description?.toLowerCase().includes(query.toLowerCase()),
			  );

	const selectedValue = value;
	const isMultiple = multiple;

	const displayValue = () => {
		if (!selectedValue) return '';

		if (isMultiple && Array.isArray(selectedValue)) {
			if (selectedValue.length === 0) return '';
			if (selectedValue.length === 1) return selectedValue[0].label;
			if (selectedValue.length <= maxDisplayedItems) {
				return selectedValue.map((item) => item.label).join(', ');
			}
			return `${selectedValue
				.slice(0, maxDisplayedItems)
				.map((item) => item.label)
				.join(', ')} +${selectedValue.length - maxDisplayedItems}`;
		}

		return (selectedValue as SelectOption)?.label || '';
	};

	const handleClear = (e: React.MouseEvent) => {
		e.stopPropagation();
		onChange(isMultiple ? [] : null);
		setQuery('');
	};

	const handleSelect = (value: SelectOption | SelectOption[]) => {
		onChange(value);
	};

	const isSelected = (option: SelectOption) => {
		if (isMultiple && Array.isArray(selectedValue)) {
			return selectedValue.some((item) => item.id === option.id);
		}
		return (selectedValue as SelectOption)?.id === option.id;
	};

	return (
		<Combobox
			value={selectedValue}
			onChange={handleSelect}
			disabled={disabled}
			multiple={isMultiple}>
			<div className={clsx('relative', className)}>
				<div className='relative'>
					<ComboboxInput
						className={clsx(
							'block w-full rounded-md pr-10',
							'transition-all duration-200 ease-in-out',
							'focus:outline-none',
							'placeholder:text-muted',
							'disabled:cursor-not-allowed disabled:opacity-50',
							'disabled:bg-surface',
							selectSizes[size],
							selectVariants[variant],
						)}
						displayValue={displayValue}
						onChange={(event) => {
							const newQuery = event.target.value;
							setQuery(newQuery);
							onInputChange?.(newQuery);
						}}
						placeholder={
							isMultiple &&
							Array.isArray(selectedValue) &&
							selectedValue.length > 0
								? searchPlaceholder
								: placeholder
						}
						disabled={disabled}
					/>

					<div className='absolute inset-y-0 right-0 flex items-center'>
						{clearable && selectedValue && (
							<button
								type='button'
								onClick={handleClear}
								className='flex items-center justify-center w-6 h-6 mr-1 text-muted hover:text-foreground rounded transition-colors'
								disabled={disabled}>
								<XMarkIcon className='w-4 h-4' />
							</button>
						)}

						<ComboboxButton className='flex items-center justify-center w-8 h-8 text-muted hover:text-foreground transition-colors'>
							<ChevronUpDownIcon className='w-4 h-4' />
						</ComboboxButton>
					</div>
				</div>

				<Transition
					as={Fragment}
					leave='transition ease-in duration-100'
					leaveFrom='opacity-100'
					leaveTo='opacity-0'
					afterLeave={() => setQuery('')}>
					<ComboboxOptions className='absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-surface border border-border-secondary shadow-lg ring-1 ring-border-muted/20 focus:outline-none'>
						{loading && (
							<div className='relative cursor-default select-none py-2 px-4 text-muted'>
								<div className='flex items-center space-x-2'>
									<div className='animate-spin h-4 w-4 border-2 border-border-secondary border-t-primary rounded-full'></div>
									<span>Loading...</span>
								</div>
							</div>
						)}

						{!loading && filteredOptions.length === 0 && query !== '' && (
							<div className='relative cursor-default select-none py-2 px-4 text-muted'>
								{emptyMessage}
							</div>
						)}

						{!loading &&
							filteredOptions.map((option) => (
								<ComboboxOption
									key={option.id}
									className={({ focus, selected }) =>
										clsx(
											'relative cursor-default select-none py-2 pl-3 pr-9',
											focus && 'bg-primary/10 text-foreground',
											selected && 'bg-primary/20',
											option.disabled && 'opacity-50 cursor-not-allowed',
										)
									}
									value={option}
									disabled={option.disabled}>
									{({ focus, selected }) => (
										<>
											<div className='flex items-center space-x-3'>
												{option.avatar && (
													<Image
														src={option.avatar}
														alt=''
														className='h-6 w-6 rounded-full object-cover flex-shrink-0'
													/>
												)}
												<div className='flex-1 min-w-0'>
													<span
														className={clsx(
															'block truncate',
															(selected || isSelected(option)) && 'font-medium',
														)}>
														{option.label}
													</span>
													{option.description && (
														<span className='block text-sm text-muted truncate'>
															{option.description}
														</span>
													)}
												</div>
											</div>

											{(selected || isSelected(option)) && (
												<span
													className={clsx(
														'absolute inset-y-0 right-0 flex items-center pr-3',
														focus ? 'text-foreground' : 'text-primary',
													)}>
													<CheckIcon className='h-4 w-4' />
												</span>
											)}
										</>
									)}
								</ComboboxOption>
							))}
					</ComboboxOptions>
				</Transition>

				{/* Selected items display for multiple selection */}
				{isMultiple &&
					Array.isArray(selectedValue) &&
					selectedValue.length > 0 && (
						<div className='mt-2 flex flex-wrap gap-1'>
							{selectedValue.map((item) => (
								<span
									key={item.id}
									className='inline-flex items-center gap-1 px-2 py-1 text-xs bg-primary/10 text-primary rounded-md border border-primary/20'>
									{item.avatar && (
										<Image
											src={item.avatar}
											alt=''
											className='h-4 w-4 rounded-full object-cover'
										/>
									)}
									<span className='truncate max-w-[120px]'>{item.label}</span>
									<button
										type='button'
										onClick={(e) => {
											e.stopPropagation();
											onChange(
												selectedValue.filter(
													(selected) => selected.id !== item.id,
												),
											);
										}}
										className='text-primary/70 hover:text-primary transition-colors'
										disabled={disabled}>
										<XMarkIcon className='h-3 w-3' />
									</button>
								</span>
							))}
						</div>
					)}
			</div>
		</Combobox>
	);
};

export default Select;
