import { clsx } from 'clsx';
import { Typography } from '../atoms';
import SidebarItem, { SidebarItemProps } from './SidebarItem';

export interface SidebarSectionProps {
	title: string;
	items?: SidebarItemProps[];
	isCollapsed?: boolean;
}

const SidebarSection: React.FC<SidebarSectionProps> = ({
	title,
	items,
	isCollapsed = false,
}) => {
	if (!items || items.length === 0) {
		return null;
	}

	return (
		<section className={clsx('px-3 py-3')}>
			{!isCollapsed && title && (
				<div className='mb-3 px-1'>
					<Typography
						variant='caption'
						color='tertiary'
						weight='semibold'
						className='tracking-wide uppercase text-xs'>
						{title}
					</Typography>
				</div>
			)}
			<nav className='space-y-1'>
				{items.map((item, index) => (
					<SidebarItem
						key={item.label || index}
						isCollapsed={isCollapsed}
						{...item}
					/>
				))}
			</nav>
		</section>
	);
};

SidebarSection.displayName = 'SidebarSection';
export default SidebarSection;
