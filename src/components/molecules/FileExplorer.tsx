import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Button } from '@/components/atoms';
import { useFileOperations } from '@/hooks/useFileOperations';
import {
	FolderIcon,
	DocumentIcon,
	ChevronRightIcon,
	ChevronDownIcon,
	ChevronLeftIcon,
} from '@heroicons/react/24/outline';
import type { FileSystemNode } from '@/types/generation';

interface FileExplorerProps {
	className?: string;
	showHeader?: boolean;
	collapsible?: boolean;
}

interface FileNodeProps {
	file: FileSystemNode;
	level: number;
	isActive: boolean;
	onSelect: (fileId: string) => void;
	isExpanded?: boolean;
	onToggleExpand?: (fileId: string) => void;
}

const FileNode: React.FC<FileNodeProps> = ({
	file,
	level,
	isActive,
	onSelect,
	isExpanded = true,
	onToggleExpand,
}) => {
	const paddingLeft = `${level * 12 + 8}px`;

	const handleClick = () => {
		if (file.type === 'folder' && onToggleExpand) {
			onToggleExpand(file.id);
		} else if (file.type === 'file') {
			onSelect(file.id);
		}
	};

	const getFileIcon = () => {
		if (file.type === 'folder') {
			return <FolderIcon className='w-4 h-4 text-blue-500' />;
		}

		// File type specific icons
		const extension = file.name.split('.').pop()?.toLowerCase();
		switch (extension) {
			case 'tsx':
			case 'ts':
				return <DocumentIcon className='w-4 h-4 text-blue-600' />;
			case 'css':
				return <DocumentIcon className='w-4 h-4 text-pink-500' />;
			case 'json':
				return <DocumentIcon className='w-4 h-4 text-yellow-600' />;
			case 'html':
				return <DocumentIcon className='w-4 h-4 text-orange-500' />;
			case 'js':
				return <DocumentIcon className='w-4 h-4 text-yellow-500' />;
			default:
				return <DocumentIcon className='w-4 h-4 text-gray-500' />;
		}
	};

	const getFileStatus = () => {
		// You can add file status indicators here (dirty, synced, etc.)
		return null;
	};

	return (
		<div>
			<button
				onClick={handleClick}
				className={`w-full flex items-center gap-2 py-1.5 px-2 text-sm rounded-md transition-colors hover:bg-surface-100 ${
					isActive && file.type === 'file'
						? 'bg-primary/10 text-primary'
						: 'text-foreground'
				}`}
				style={{ paddingLeft }}>
				{/* Expand/Collapse Icon for folders */}
				{file.type === 'folder' && (
					<div className='w-4 h-4 flex items-center justify-center'>
						{isExpanded ? (
							<ChevronDownIcon className='w-3 h-3' />
						) : (
							<ChevronRightIcon className='w-3 h-3' />
						)}
					</div>
				)}

				{/* File/Folder Icon */}
				{getFileIcon()}

				{/* File Name */}
				<span className='flex-1 text-left truncate'>{file.name}</span>

				{/* File Status */}
				{getFileStatus()}
			</button>

			{/* Children (for folders) */}
			{file.type === 'folder' && isExpanded && file.children && (
				<div>
					{file.children.map((child) => (
						<FileNode
							key={child.id}
							file={child}
							level={level + 1}
							isActive={isActive}
							onSelect={onSelect}
							isExpanded={isExpanded}
							onToggleExpand={onToggleExpand}
						/>
					))}
				</div>
			)}
		</div>
	);
};

export const FileExplorer: React.FC<FileExplorerProps> = ({
	className = '',
	showHeader = true,
	collapsible = false,
}) => {
	const {
		files,
		activeFileId,
		setActiveFile,
		getEditableFiles,
		hasUnsavedChanges,
	} = useFileOperations();

	const [expandedFolders, setExpandedFolders] = useState<Set<string>>(
		new Set(['src-folder']), // Expand src folder by default
	);
	const [isCollapsed, setIsCollapsed] = useState(false);

	// Toggle folder expansion
	const handleToggleExpand = (folderId: string) => {
		setExpandedFolders((prev) => {
			const newSet = new Set(prev);
			if (newSet.has(folderId)) {
				newSet.delete(folderId);
			} else {
				newSet.add(folderId);
			}
			return newSet;
		});
	};

	// Get file statistics
	const fileStats = {
		total: files.length,
		editable: getEditableFiles().length,
		hasChanges: files.some((f) => hasUnsavedChanges(f.id)),
	};

	// Organize files into tree structure
	const organizeFilesIntoTree = (files: FileSystemNode[]): FileSystemNode[] => {
		const tree: FileSystemNode[] = [];
		const folderMap = new Map<string, FileSystemNode>();

		// First pass: create folders and collect files
		files.forEach((file) => {
			if (file.type === 'folder') {
				folderMap.set(file.id, { ...file, children: [] });
			}
		});

		// Second pass: organize files into tree
		files.forEach((file) => {
			if (file.type === 'file') {
				const pathParts = file.path.split('/');
				if (pathParts.length > 1) {
					// File is in a folder
					const folderName = pathParts[0];
					const folder = Array.from(folderMap.values()).find(
						(f) => f.name === `${folderName}/`,
					);
					if (folder && folder.children) {
						folder.children.push(file);
					}
				} else {
					// Root level file
					tree.push(file);
				}
			}
		});

		// Add folders to tree
		folderMap.forEach((folder) => {
			tree.push(folder);
		});

		// Sort: folders first, then files alphabetically
		return tree.sort((a, b) => {
			if (a.type !== b.type) {
				return a.type === 'folder' ? -1 : 1;
			}
			return a.name.localeCompare(b.name);
		});
	};

	const treeFiles = organizeFilesIntoTree(files);

	if (isCollapsed && collapsible) {
		return (
			<div
				className={`border-r border-border-secondary bg-surface-50 ${className}`}>
				<button
					onClick={() => setIsCollapsed(false)}
					className='w-full p-3 hover:bg-surface-100 transition-colors border-b border-border-secondary'
					title='Expand file explorer'>
					<ChevronRightIcon className='w-4 h-4 mx-auto text-muted' />
				</button>
			</div>
		);
	}

	return (
		<div
			className={`w-full h-full border-r border-border-secondary bg-surface-50 flex flex-col ${className}`}>
			{/* Header */}
			{showHeader && (
				<div className='p-4 border-b border-border-secondary'>
					<div className='flex items-center justify-between mb-2'>
						<Typography
							variant='body-sm'
							weight='semibold'>
							Project Files
						</Typography>

						{collapsible && (
							<Button
								variant='ghost'
								size='sm'
								onClick={() => setIsCollapsed(true)}
								className='p-1'
								title='Collapse file explorer'>
								<ChevronLeftIcon className='w-4 h-4' />
							</Button>
						)}
					</div>

					<div className='flex items-center gap-4 text-xs text-muted'>
						<span>{fileStats.total} files</span>
						{fileStats.hasChanges && (
							<span className='text-warning'>• Unsaved changes</span>
						)}
					</div>
				</div>
			)}

			{/* File Tree */}
			<div className='flex-1 overflow-y-auto p-2'>
				{treeFiles.length > 0 ? (
					<div className='space-y-0.5'>
						{treeFiles.map((file) => (
							<FileNode
								key={file.id}
								file={file}
								level={0}
								isActive={activeFileId === file.id}
								onSelect={setActiveFile}
								isExpanded={expandedFolders.has(file.id)}
								onToggleExpand={handleToggleExpand}
							/>
						))}
					</div>
				) : (
					<div className='flex items-center justify-center h-32'>
						<Typography
							variant='body-sm'
							color='tertiary'>
							No files available
						</Typography>
					</div>
				)}
			</div>

			{/* Footer */}
			<div className='p-2 border-t border-border-secondary'>
				<Typography
					variant='caption'
					color='tertiary'
					className='text-center'>
					{fileStats.editable} editable files
				</Typography>
			</div>
		</div>
	);
};
