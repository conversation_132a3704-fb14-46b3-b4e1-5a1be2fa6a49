import React, { useCallback, useEffect, useState } from 'react';
import { Typo<PERSON>, Button, MonacoEditor } from '@/components/atoms';
import { useFileOperations } from '@/hooks/useFileOperations';
import { useGenerationStore } from '@/stores/generationStore';
import { stripMarkdownCodeBlocks } from '@/utils/markdown';
import { detectFileLanguage } from '@/utils/fileLanguage';
import {
	DocumentDuplicateIcon,
	CheckIcon,
	ArrowPathIcon,
	ExclamationTriangleIcon,
	DocumentIcon,
} from '@heroicons/react/24/outline';

interface CodeEditorProps {
	className?: string;
	showActions?: boolean;
	onCodeChange?: (fileId: string, content: string) => void;
}

export const CodeEditor: React.FC<CodeEditorProps> = ({
	className = '',
	showActions = true,
	onCodeChange,
}) => {
	const {
		activeFileId,
		activeFile,
		updateFileContent,
		resetFileContent,
		getCurrentFileContent,
		copyFileContent,
		hasUnsavedChanges,
	} = useFileOperations();

	const { isStreaming, streamingContent, copiedFileId, setCopiedFile } =
		useGenerationStore();

	const [autoSaveTimeout, setAutoSaveTimeout] = useState<NodeJS.Timeout | null>(
		null,
	);

	// Get current file content with streaming support
	const getCurrentContent = useCallback(() => {
		if (activeFile?.id === 'app-tsx' && isStreaming) {
			return stripMarkdownCodeBlocks(streamingContent);
		}
		// For documentation files, don't strip markdown
		if (activeFile?.language === 'markdown') {
			return getCurrentFileContent(activeFileId);
		}
		return getCurrentFileContent(activeFileId);
	}, [
		activeFile,
		activeFileId,
		isStreaming,
		streamingContent,
		getCurrentFileContent,
	]);

	// Get proper language for Monaco Editor with comprehensive detection
	const getMonacoLanguage = useCallback(
		(file: typeof activeFile) => {
			if (!file) return 'typescript';

			const content = getCurrentContent();
			return detectFileLanguage(file.name, content, file.language);
		},
		[getCurrentContent],
	);

	// Handle content changes with auto-save
	const handleContentChange = useCallback(
		(content: string | undefined) => {
			if (!content || !activeFileId || !activeFile?.isEditable) return;

			// Update content in store
			updateFileContent(activeFileId, content);

			// Call parent callback
			onCodeChange?.(activeFileId, content);

			// Auto-save after 2 seconds of inactivity
			if (autoSaveTimeout) {
				clearTimeout(autoSaveTimeout);
			}

			const timeout = setTimeout(() => {
				// Here you could implement auto-save to backend
				console.log('Auto-save triggered for file:', activeFileId);
			}, 2000);

			setAutoSaveTimeout(timeout);
		},
		[
			activeFileId,
			activeFile,
			updateFileContent,
			onCodeChange,
			autoSaveTimeout,
		],
	);

	// Cleanup auto-save timeout
	useEffect(() => {
		return () => {
			if (autoSaveTimeout) {
				clearTimeout(autoSaveTimeout);
			}
		};
	}, [autoSaveTimeout]);

	// Handle copy action
	const handleCopy = useCallback(async () => {
		if (!activeFileId) return;

		const success = await copyFileContent(activeFileId);
		if (success) {
			setCopiedFile(activeFileId);
			setTimeout(() => setCopiedFile(null), 2000);
		}
	}, [activeFileId, copyFileContent, setCopiedFile]);

	// Handle reset action
	const handleReset = useCallback(() => {
		if (!activeFileId) return;
		resetFileContent(activeFileId);
	}, [activeFileId, resetFileContent]);

	// Render file tabs - REMOVED
	const renderTabs = () => {
		return null;
	};

	// Render action buttons
	const renderActions = () => {
		if (!showActions || !activeFile) return null;

		return (
			<div className='absolute top-4 right-4 z-10 flex items-center gap-2'>
				{/* Reset button (if file has changes) */}
				{hasUnsavedChanges(activeFileId) && (
					<Button
						variant='ghost'
						size='sm'
						onClick={handleReset}
						className='bg-surface-50/80 backdrop-blur-sm border border-border-secondary'
						title='Reset to original'>
						<ArrowPathIcon className='w-4 h-4' />
					</Button>
				)}

				{/* Copy button */}
				<Button
					variant='ghost'
					size='sm'
					onClick={handleCopy}
					className='bg-surface-50/80 backdrop-blur-sm border border-border-secondary'>
					{copiedFileId === activeFileId ? (
						<>
							<CheckIcon className='w-4 h-4 mr-2' />
							Copied
						</>
					) : (
						<>
							<DocumentDuplicateIcon className='w-4 h-4 mr-2' />
							Copy
						</>
					)}
				</Button>
			</div>
		);
	};

	// Render editor content
	const renderEditor = () => {
		if (!activeFile) {
			return (
				<div className='h-full flex items-center justify-center'>
					<div className='text-center'>
						<ExclamationTriangleIcon className='w-16 h-16 text-muted mx-auto mb-4' />
						<Typography
							variant='h4'
							color='secondary'>
							No File Selected
						</Typography>
						<Typography
							variant='body'
							color='tertiary'>
							Select a file from the explorer to start editing
						</Typography>
					</div>
				</div>
			);
		}

		if (!activeFile.isEditable) {
			return (
				<div className='h-full flex items-center justify-center'>
					<div className='text-center'>
						<DocumentIcon className='w-16 h-16 text-muted mx-auto mb-4' />
						<Typography
							variant='h4'
							color='secondary'>
							Read-Only File
						</Typography>
						<Typography
							variant='body'
							color='tertiary'>
							This file is not editable
						</Typography>
					</div>
				</div>
			);
		}

		return (
			<MonacoEditor
				key={activeFile.id} // Force re-render when file changes
				value={
					// For App.tsx during streaming, don't set value to allow streaming
					activeFile.id === 'app-tsx' && isStreaming
						? undefined
						: getCurrentContent()
				}
				streamingValue={
					// Only stream for App.tsx
					activeFile.id === 'app-tsx' && isStreaming
						? stripMarkdownCodeBlocks(streamingContent)
						: undefined
				}
				enableStreaming={activeFile.id === 'app-tsx' && isStreaming}
				streamingDelay={30}
				language={getMonacoLanguage(activeFile)}
				theme='vs-dark'
				height='100%'
				readOnly={false}
				className='h-full w-full'
				onChange={handleContentChange}
				options={{
					minimap: { enabled: false },
					scrollBeyondLastLine: false,
					fontSize: 14,
					lineNumbers: 'on',
					wordWrap: 'on',
					automaticLayout: true,
					tabSize: 2,
					insertSpaces: true,
					formatOnPaste: true,
					formatOnType: true,
				}}
			/>
		);
	};

	return (
		<div className={`flex flex-col h-full w-full overflow-hidden ${className}`}>
			{/* File Tabs */}
			{renderTabs()}

			{/* Editor Container */}
			<div className='flex-1 relative'>
				{/* Action Buttons */}
				{renderActions()}

				{/* Editor */}
				{renderEditor()}
			</div>

			{/* Status Bar */}
			{activeFile && (
				<div className='px-4 py-2 border-t border-border-secondary bg-surface-50 flex items-center justify-between'>
					<div className='flex items-center gap-4 text-sm text-muted'>
						<span>{activeFile.name}</span>
						<span>{activeFile.language}</span>
						{hasUnsavedChanges(activeFileId) && (
							<span className='text-warning'>• Unsaved changes</span>
						)}
					</div>

					{isStreaming && activeFile.id === 'app-tsx' && (
						<div className='flex items-center gap-2 text-sm text-primary'>
							<div className='w-2 h-2 bg-primary rounded-full animate-pulse' />
							<span>Streaming...</span>
						</div>
					)}
				</div>
			)}
		</div>
	);
};
