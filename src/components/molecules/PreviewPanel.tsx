import React, { useCallback, useEffect } from 'react';
import { Typo<PERSON>, But<PERSON> } from '@/components/atoms';
import { useWebContainerIntegration } from '@/hooks/useWebContainerIntegration';
import { DocumentationRenderer } from './DocumentationRenderer';
import {
	PlayIcon,
	StopIcon,
	ArrowPathIcon,
	ExclamationTriangleIcon,
	CpuChipIcon,
	CheckCircleIcon,
} from '@heroicons/react/24/outline';

interface PreviewPanelProps {
	className?: string;
	showControls?: boolean;
	autoStart?: boolean;
	generationType?: 'UI' | 'DOCUMENTATION';
	documentationContent?: string;
	isStreaming?: boolean;
	streamingContent?: string;
}

export const PreviewPanel: React.FC<PreviewPanelProps> = ({
	className = '',
	showControls = true,
	autoStart = false,
	generationType = 'UI',
	documentationContent = '',
	isStreaming = false,
	streamingContent = '',
}) => {
	// Only use WebContainer for UI generations
	const {
		webContainerState,
		isReady,
		isBooting,
		isInstalling,
		isStarting,
		error,
		url,
		initializeWebContainer,
		updateAllFilesInWebContainer,
		restartPreview,
		stopDevServer,
		isWebContainerOperational,
	} = useWebContainerIntegration(generationType === 'UI');

	// Removed unused variables

	// Auto-start WebContainer if requested (only for UI generations)
	useEffect(() => {
		if (
			generationType === 'UI' &&
			autoStart &&
			isReady &&
			!webContainerState.hasInitialized
		) {
			initializeWebContainer();
		}
	}, [
		generationType,
		autoStart,
		isReady,
		webContainerState.hasInitialized,
		initializeWebContainer,
	]);

	// Handle starting the preview
	const handleStart = useCallback(async () => {
		if (!webContainerState.hasInitialized) {
			await initializeWebContainer();
		} else {
			await updateAllFilesInWebContainer();
		}
	}, [
		webContainerState.hasInitialized,
		initializeWebContainer,
		updateAllFilesInWebContainer,
	]);

	// Handle stopping the preview
	const handleStop = useCallback(() => {
		stopDevServer();
	}, [stopDevServer]);

	// Handle refreshing the preview
	const handleRefresh = useCallback(async () => {
		await restartPreview();
	}, [restartPreview]);

	// Get current status
	const getStatus = () => {
		if (error) return { type: 'error', message: error };
		if (isBooting)
			return { type: 'loading', message: 'Booting WebContainer...' };
		if (isInstalling)
			return { type: 'loading', message: 'Installing dependencies...' };
		if (isStarting)
			return { type: 'loading', message: 'Starting dev server...' };
		if (url && webContainerState.hasInitialized)
			return { type: 'ready', message: 'Preview ready' };
		if (isReady) return { type: 'idle', message: 'Ready to start preview' };
		return { type: 'loading', message: 'Initializing...' };
	};

	const status = getStatus();
	const isLoading = isBooting || isInstalling || isStarting;
	const canStart = isReady && !isLoading && !webContainerState.hasInitialized;
	const canStop = webContainerState.hasInitialized && !isLoading;
	const canRefresh = isWebContainerOperational() && !isLoading;

	// Render status indicator
	const renderStatusIndicator = () => {
		const statusConfig = {
			error: {
				icon: ExclamationTriangleIcon,
				color: 'text-error',
				bgColor: 'bg-error/10',
			},
			loading: {
				icon: CpuChipIcon,
				color: 'text-primary',
				bgColor: 'bg-primary/10',
			},
			ready: {
				icon: CheckCircleIcon,
				color: 'text-success',
				bgColor: 'bg-success/10',
			},
			idle: { icon: PlayIcon, color: 'text-muted', bgColor: 'bg-surface-100' },
		};

		const config = statusConfig[status.type as keyof typeof statusConfig];
		const Icon = config.icon;

		return (
			<div
				className={`flex items-center gap-2 px-3 py-1.5 rounded-md ${config.bgColor}`}>
				<Icon
					className={`w-4 h-4 ${config.color} ${
						isLoading ? 'animate-spin' : ''
					}`}
				/>
				<Typography
					variant='body-sm'
					className={config.color}>
					{status.message}
				</Typography>
			</div>
		);
	};

	// Render control buttons
	const renderControls = () => {
		if (!showControls) return null;

		return (
			<div className='flex items-center gap-2'>
				{!webContainerState.hasInitialized ? (
					<Button
						variant='primary'
						size='sm'
						onClick={handleStart}
						disabled={!canStart}
						className='flex items-center gap-2'>
						{isLoading ? (
							<ArrowPathIcon className='w-4 h-4 animate-spin' />
						) : (
							<PlayIcon className='w-4 h-4' />
						)}
						{isLoading ? 'Starting...' : 'Start Preview'}
					</Button>
				) : (
					<>
						<Button
							variant='ghost'
							size='sm'
							onClick={handleRefresh}
							disabled={!canRefresh}
							className='flex items-center gap-2'>
							<ArrowPathIcon className='w-4 h-4' />
							Refresh
						</Button>
						<Button
							variant='secondary'
							size='sm'
							onClick={handleStop}
							disabled={!canStop}
							className='flex items-center gap-2'>
							<StopIcon className='w-4 h-4' />
							Stop
						</Button>
					</>
				)}
			</div>
		);
	};

	// Render preview content
	const renderPreviewContent = () => {
		// Handle documentation type
		if (generationType === 'DOCUMENTATION') {
			return (
				<DocumentationRenderer
					content={documentationContent}
					isStreaming={isStreaming}
					streamingContent={streamingContent}
					showHeader={false}
					className='h-full w-full'
				/>
			);
		}

		// Handle UI type (WebContainer)
		if (error) {
			return (
				<div className='h-full flex items-center justify-center p-8'>
					<div className='text-center max-w-md'>
						<div className='w-16 h-16 bg-error/10 rounded-lg flex items-center justify-center mx-auto mb-4'>
							<ExclamationTriangleIcon className='w-8 h-8 text-error' />
						</div>
						<Typography
							variant='h4'
							className='mb-2 text-error'>
							Preview Error
						</Typography>
						<Typography
							variant='body'
							color='secondary'
							className='mb-4'>
							{error}
						</Typography>
						<Button
							variant='secondary'
							size='sm'
							onClick={handleStart}>
							Try Again
						</Button>
					</div>
				</div>
			);
		}

		if (url && webContainerState.hasInitialized) {
			return (
				<div className='w-full h-full overflow-hidden'>
					<iframe
						src={url}
						className='w-full h-full border-0'
						title='Live Preview'
						sandbox='allow-scripts allow-same-origin allow-forms allow-popups allow-modals'
					/>
				</div>
			);
		}

		if (isLoading) {
			return (
				<div className='h-full flex items-center justify-center'>
					<div className='text-center'>
						<div className='w-16 h-16 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4'>
							<ArrowPathIcon className='w-8 h-8 text-primary animate-spin' />
						</div>
						<Typography
							variant='h4'
							color='secondary'
							className='mb-2'>
							{status.message}
						</Typography>
						<Typography
							variant='body'
							color='tertiary'>
							This may take a few moments
						</Typography>
					</div>
				</div>
			);
		}

		return (
			<div className='h-full flex items-center justify-center'>
				<div className='text-center p-8'>
					<div className='w-16 h-16 bg-surface-100 rounded-lg flex items-center justify-center mx-auto mb-4'>
						<PlayIcon className='w-8 h-8 text-muted' />
					</div>
					<Typography
						variant='h4'
						color='secondary'
						className='mb-2'>
						Ready to Preview
					</Typography>
					<Typography
						variant='body'
						color='tertiary'
						className='mb-4 max-w-md'>
						Click &quot;Start Preview&quot; to run your generated code in a live
						environment.
					</Typography>
					<Button
						variant='primary'
						onClick={handleStart}
						disabled={!canStart}>
						Start Preview
					</Button>
				</div>
			</div>
		);
	};

	return (
		<div className={`h-full flex flex-col w-full ${className}`}>
			{/* Header */}
			<div className='flex items-center justify-between p-4 border-b border-border-secondary bg-surface-50'>
				<div className='flex items-center gap-3'>
					<Typography
						variant='body-sm'
						weight='semibold'
						color='secondary'>
						{generationType === 'DOCUMENTATION'
							? 'Documentation Preview'
							: 'Live Preview'}
					</Typography>
					<span className='px-2 py-1 text-xs font-medium bg-primary/10 text-primary rounded-md'>
						{generationType === 'DOCUMENTATION'
							? 'Markdown'
							: 'React + Vite + Tailwind'}
					</span>
				</div>

				{generationType === 'UI' && renderControls()}
			</div>

			{/* Status Bar - Only for UI mode */}
			{generationType === 'UI' && (
				<div className='px-4 py-2 border-b border-border-secondary bg-surface-25'>
					{renderStatusIndicator()}
				</div>
			)}

			{/* Preview Content */}
			<div className='flex-1 relative overflow-hidden'>
				{renderPreviewContent()}
			</div>
		</div>
	);
};
