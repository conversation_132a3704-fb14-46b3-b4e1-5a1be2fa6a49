import React, { useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import { Typography } from '@/components/atoms';
import {
	DocumentTextIcon,
	ClipboardDocumentIcon,
	CheckIcon,
} from '@heroicons/react/24/outline';
import { useState } from 'react';

// Import highlight.js styles
import 'highlight.js/styles/github-dark.css';

interface DocumentationRendererProps {
	content: string;
	title?: string;
	showHeader?: boolean;
	showCopyButton?: boolean;
	className?: string;
	isStreaming?: boolean;
	streamingContent?: string;
}

export const DocumentationRenderer: React.FC<DocumentationRendererProps> = ({
	content,
	title,
	showHeader = true,
	showCopyButton = true,
	className = '',
	isStreaming = false,
	streamingContent = '',
}) => {
	const [copied, setCopied] = useState(false);

	// Use streaming content if available, otherwise use static content
	const displayContent =
		isStreaming && streamingContent ? streamingContent : content;

	// Custom components for react-markdown
	const components = useMemo(
		() => ({
			// Custom heading components
			h1: ({ children, ...props }: any) => (
				<Typography
					variant='h1'
					weight='bold'
					className='mb-6 mt-8 first:mt-0 text-foreground border-b border-border-secondary pb-2'
					{...props}>
					{children}
				</Typography>
			),
			h2: ({ children, ...props }: any) => (
				<Typography
					variant='h2'
					weight='bold'
					className='mb-4 mt-6 first:mt-0 text-foreground'
					{...props}>
					{children}
				</Typography>
			),
			h3: ({ children, ...props }: any) => (
				<Typography
					variant='h3'
					weight='semibold'
					className='mb-3 mt-5 first:mt-0 text-foreground'
					{...props}>
					{children}
				</Typography>
			),
			h4: ({ children, ...props }: any) => (
				<Typography
					variant='h4'
					weight='semibold'
					className='mb-2 mt-4 first:mt-0 text-foreground'
					{...props}>
					{children}
				</Typography>
			),
			h5: ({ children, ...props }: any) => (
				<Typography
					variant='h5'
					weight='semibold'
					className='mb-2 mt-3 first:mt-0 text-foreground'
					{...props}>
					{children}
				</Typography>
			),
			h6: ({ children, ...props }: any) => (
				<Typography
					variant='h6'
					weight='semibold'
					className='mb-2 mt-3 first:mt-0 text-foreground'
					{...props}>
					{children}
				</Typography>
			),

			// Custom paragraph component
			p: ({ children, ...props }: any) => (
				<Typography
					variant='body'
					className='mb-4 leading-relaxed text-foreground'
					{...props}>
					{children}
				</Typography>
			),

			// Custom list components
			ul: ({ children, ...props }: any) => (
				<ul
					className='mb-4 ml-6 list-disc space-y-1'
					{...props}>
					{children}
				</ul>
			),
			ol: ({ children, ...props }: any) => (
				<ol
					className='mb-4 ml-6 list-decimal space-y-1'
					{...props}>
					{children}
				</ol>
			),
			li: ({ children, ...props }: any) => (
				<li
					className='text-foreground leading-relaxed'
					{...props}>
					{children}
				</li>
			),

			// Custom code components
			code: ({ inline, className, children, ...props }: any) => {
				if (inline) {
					return (
						<code
							className='bg-surface-100 text-accent-purple-700 px-1.5 py-0.5 rounded text-sm font-mono border border-border-secondary'
							{...props}>
							{children}
						</code>
					);
				}
				return (
					<code
						className={`block bg-surface-100 text-foreground p-4 rounded-lg border border-border-secondary font-mono text-sm overflow-x-auto ${className}`}
						{...props}>
						{children}
					</code>
				);
			},

			// Custom pre component for code blocks
			pre: ({ children, ...props }: any) => (
				<pre
					className='mb-4 bg-surface-100 rounded-lg border border-border-secondary overflow-hidden'
					{...props}>
					{children}
				</pre>
			),

			// Custom blockquote component
			blockquote: ({ children, ...props }: any) => (
				<blockquote
					className='mb-4 pl-4 border-l-4 border-primary bg-primary/5 py-2 italic text-foreground'
					{...props}>
					{children}
				</blockquote>
			),

			// Custom table components
			table: ({ children, ...props }: any) => (
				<div className='mb-4 overflow-x-auto'>
					<table
						className='min-w-full border border-border-secondary rounded-lg'
						{...props}>
						{children}
					</table>
				</div>
			),
			thead: ({ children, ...props }: any) => (
				<thead
					className='bg-surface-100'
					{...props}>
					{children}
				</thead>
			),
			tbody: ({ children, ...props }: any) => (
				<tbody
					className='divide-y divide-border-secondary'
					{...props}>
					{children}
				</tbody>
			),
			tr: ({ children, ...props }: any) => (
				<tr
					className='hover:bg-surface-50'
					{...props}>
					{children}
				</tr>
			),
			th: ({ children, ...props }: any) => (
				<th
					className='px-4 py-2 text-left font-semibold text-foreground border-r border-border-secondary last:border-r-0'
					{...props}>
					{children}
				</th>
			),
			td: ({ children, ...props }: any) => (
				<td
					className='px-4 py-2 text-foreground border-r border-border-secondary last:border-r-0'
					{...props}>
					{children}
				</td>
			),

			// Custom link component
			a: ({ children, href, ...props }: any) => (
				<a
					href={href}
					className='text-primary hover:text-primary/80 underline transition-colors'
					target='_blank'
					rel='noopener noreferrer'
					{...props}>
					{children}
				</a>
			),

			// Custom horizontal rule
			hr: ({ ...props }: any) => (
				<hr
					className='my-6 border-border-secondary'
					{...props}
				/>
			),
		}),
		[],
	);

	const handleCopy = async () => {
		try {
			await navigator.clipboard.writeText(displayContent);
			setCopied(true);
			setTimeout(() => setCopied(false), 2000);
		} catch (error) {
			console.error('Failed to copy content:', error);
		}
	};

	return (
		<div className={`h-full w-full flex flex-col bg-surface-50 ${className}`}>
			{/* Header */}
			{showHeader && (
				<div className='flex-shrink-0 p-4 border-b border-border-secondary bg-surface-100'>
					<div className='flex items-center justify-between'>
						<div className='flex items-center gap-3'>
							<div className='p-2 bg-accent-purple-100 rounded-lg'>
								<DocumentTextIcon className='w-5 h-5 text-accent-purple-600' />
							</div>
							<div>
								<Typography
									variant='h5'
									weight='semibold'>
									{title || 'Documentation'}
								</Typography>
								<Typography
									variant='body-sm'
									color='secondary'>
									{isStreaming ? 'Generating...' : 'Generated documentation'}
								</Typography>
							</div>
						</div>

						{showCopyButton && (
							<button
								onClick={handleCopy}
								className='flex items-center gap-2 px-3 py-1.5 text-sm bg-surface-50 hover:bg-surface-100 border border-border-secondary rounded-lg transition-colors'
								title='Copy documentation'>
								{copied ? (
									<CheckIcon className='w-4 h-4 text-positive' />
								) : (
									<ClipboardDocumentIcon className='w-4 h-4 text-muted' />
								)}
								<span className='text-foreground'>
									{copied ? 'Copied!' : 'Copy'}
								</span>
							</button>
						)}
					</div>
				</div>
			)}

			{/* Content */}
			<div className='flex-1 overflow-y-auto overflow-x-hidden'>
				<div className='p-4 md:p-6'>
					<div className='max-w-none md:max-w-4xl mx-auto w-full'>
						{displayContent ? (
							<div className='prose prose-lg max-w-none'>
								<ReactMarkdown
									components={components}
									remarkPlugins={[remarkGfm]}
									rehypePlugins={[rehypeHighlight]}>
									{displayContent}
								</ReactMarkdown>
							</div>
						) : (
							<div className='text-center py-12'>
								<DocumentTextIcon className='w-12 h-12 text-muted mx-auto mb-4' />
								<Typography
									variant='body'
									color='secondary'>
									No documentation content available
								</Typography>
							</div>
						)}

						{/* Streaming indicator */}
						{isStreaming && (
							<div className='flex items-center gap-2 mt-4 text-primary'>
								<div className='w-2 h-2 bg-primary rounded-full animate-bounce' />
								<div
									className='w-2 h-2 bg-primary rounded-full animate-bounce'
									style={{ animationDelay: '0.1s' }}
								/>
								<div
									className='w-2 h-2 bg-primary rounded-full animate-bounce'
									style={{ animationDelay: '0.2s' }}
								/>
								<Typography
									variant='body-sm'
									color='primary'
									className='ml-2'>
									Generating documentation...
								</Typography>
							</div>
						)}
					</div>
				</div>
			</div>
		</div>
	);
};
