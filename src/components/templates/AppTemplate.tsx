import { useAuthStore } from '@/providers/auth-store-provider';
import Menubar from '../organisms/Menubar';

export default function AppTemplate({
	children,
}: {
	children: React.ReactNode;
}) {
	const user = useAuthStore((state) => state.user);

	// Base links available to all users
	const baseLinks = [
		{
			title: 'Home',
			href: '/',
			startIcon: null,
			endIcon: null,
			scope: 'user',
			onClick: () => {},
			className: '',
			disabled: false,
		},
		{
			title: 'Projects',
			href: '/projects',
			startIcon: null,
			endIcon: null,
			scope: 'user',
			onClick: () => {},
			className: '',
			disabled: false,
		},
		{
			title: 'Generations',
			href: '/generations',
			startIcon: null,
			endIcon: null,
			scope: 'user',
			onClick: () => {},
			className: '',
			disabled: false,
		},
	];

	// Admin-only links
	const adminLinks = [
		{
			title: 'User Management',
			href: '/admin/users',
			startIcon: null,
			endIcon: null,
			scope: 'admin',
			onClick: () => {},
			className: '',
			disabled: false,
		},
	];

	// Combine links based on user role
	const links =
		user?.role === 'ADMIN' ? [...baseLinks, ...adminLinks] : baseLinks;
	// No manual authentication check needed - withAuth HOC handles this
	// Middleware also handles authentication at the route level
	return (
		<div className='flex min-h-screen flex-col bg-base text-foreground'>
			<Menubar
				logo='PixiGenerator'
				links={links}
			/>
			<main className='flex-1'>{children}</main>
		</div>
	);
}
