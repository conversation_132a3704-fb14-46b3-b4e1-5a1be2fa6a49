import { useState } from 'react';

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>po<PERSON> } from '@/components/atoms';
import type { Project } from '@/components/templates/project/ProjectsGrid';
import { Dialog } from '@/components/molecules';

interface DeleteProjectDialogProps {
	isOpen: boolean;
	onClose: () => void;
	project: Project | null;
	onProjectDeleted?: (id: string) => Promise<boolean>;
}

const DeleteProjectDialog: React.FC<DeleteProjectDialogProps> = ({
	isOpen,
	onClose,
	project,
	onProjectDeleted,
}) => {
	const [isDeleting, setIsDeleting] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const handleDelete = async () => {
		if (!project) {
			setError('No project selected for deletion');
			return;
		}

		setError(null);
		setIsDeleting(true);

		try {
			if (onProjectDeleted) {
				const success = await onProjectDeleted(project.id);
				if (success) {
				} else {
					setError('Failed to delete project');
				}
			}
		} catch (error) {
			console.error('Error deleting project:', error);
			if (error instanceof Error) {
				setError(`Failed to delete project: ${error.message}`);
			} else {
				setError(
					'Failed to delete project: Network error or server unavailable',
				);
			}
		} finally {
			setIsDeleting(false);
		}
	};

	const handleClose = () => {
		if (!isDeleting) {
			setError(null);
			onClose();
		}
	};

	return (
		<Dialog
			isOpen={isOpen}
			onClose={handleClose}
			title='Delete Project'
			description='This action cannot be undone.'
			size='md'>
			{error && (
				<Alert
					variant='negative'
					className='mb-3'>
					{error}
				</Alert>
			)}

			<div className='space-y-3'>
				<Typography
					variant='body'
					color='secondary'>
					Are you sure you want to delete the project{' '}
					<span className='font-medium text-foreground'>
						&ldquo;{project?.name}&rdquo;
					</span>
					? This action cannot be undone and all project data will be
					permanently removed.
				</Typography>

				<div className='flex flex-col sm:flex-row justify-end gap-1.5 pt-2'>
					<Button
						type='button'
						variant='secondary'
						onClick={handleClose}
						disabled={isDeleting}
						className='order-2 sm:order-1'>
						Cancel
					</Button>
					<Button
						type='button'
						variant='danger'
						onClick={handleDelete}
						disabled={isDeleting}
						className='order-1 sm:order-2'>
						{isDeleting && <ButtonLoader />}
						{isDeleting ? 'Deleting...' : 'Delete Project'}
					</Button>
				</div>
			</div>
		</Dialog>
	);
};

export default DeleteProjectDialog;
