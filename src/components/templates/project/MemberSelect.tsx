import { Field, Label } from '@headlessui/react';
import { UserIcon } from '@heroicons/react/24/outline';

import { Select, Typography } from '@/components/atoms';
import type { SelectOption } from '@/components/atoms/Select';
import { useMemberSelection } from '@/hooks/useMemberSelection';
import type { User } from '@/api/UserApi';

interface MemberSelectProps {
	value: User[];
	onChange: (members: User[]) => void;
	disabled?: boolean;
	className?: string;
}

const MemberSelect: React.FC<MemberSelectProps> = ({
	value,
	onChange,
	disabled = false,
	className,
}) => {
	const { users, loading, userOptions, selectedOptions } =
		useMemberSelection(value);

	const handleSelectionChange = (
		selectedOptions: SelectOption | SelectOption[] | null,
	) => {
		if (!selectedOptions) {
			onChange([]);
			return;
		}

		const optionsArray = Array.isArray(selectedOptions)
			? selectedOptions
			: [selectedOptions];

		const selectedUsers: User[] = optionsArray.map((option) => {
			const foundUser = users.find((user) => user.id === option.id);
			if (foundUser) {
				return foundUser;
			}

			const createdUser: User = {
				id: option.id as string,
				fullName: option.label,
				email: option.description || '',
				avatar: option.avatar,
				role: 'USER' as const,
				status: option.disabled !== true ? 'ACTIVE' : ('INACTIVE' as const),
			};
			return createdUser;
		});

		onChange(selectedUsers);
	};

	const handleInputChange = async (query: string) => {
		console.log('Search query:', query);
	};

	return (
		<Field className={className}>
			<Label htmlFor='members'>
				<Typography
					variant='body-sm'
					weight='medium'
					color='secondary'>
					Team Members
				</Typography>
			</Label>
			<div className='mt-1'>
				<Select
					options={userOptions}
					value={selectedOptions}
					onChange={handleSelectionChange}
					onInputChange={handleInputChange}
					placeholder='Search and select team members...'
					searchPlaceholder='Type to search users...'
					multiple
					clearable
					disabled={disabled}
					loading={loading}
					emptyMessage='No users found. Try searching for names or emails.'
					maxDisplayedItems={1}
					size='md'
				/>
			</div>
			{selectedOptions.length > 0 && (
				<div className='mt-2'>
					<Typography
						variant='caption'
						color='tertiary'
						className='flex items-center gap-1'>
						<UserIcon className='w-3 h-3' />
						{selectedOptions.length} member
						{selectedOptions.length !== 1 ? 's' : ''} selected
					</Typography>
				</div>
			)}
		</Field>
	);
};

export default MemberSelect;
