import { Field, Label, Fieldset } from '@headlessui/react';

import {
	Input,
	Textarea,
	Button,
	Alert,
	ButtonLoa<PERSON>,
	Typography,
} from '@/components/atoms';
import { Dialog } from '@/components/molecules';

import { useProjectForm } from '@/hooks/useProjectForm';
import type { Project } from '@/components/templates/project/ProjectsGrid';
import MemberSelect from './MemberSelect';

interface ProjectFormDialogProps {
	isOpen: boolean;
	onClose: () => void;
	mode: 'create' | 'edit';
	project?: Project | null;
	onProjectCreated?: (projectData: {
		name: string;
		description: string;
	}) => Promise<Project | null>;
	onProjectUpdated?: (
		id: string,
		projectData: { name: string; description: string },
	) => Promise<Project | null>;
}

const ProjectFormDialog: React.FC<ProjectFormDialogProps> = ({
	isOpen,
	onClose,
	mode,
	project,
	onProjectCreated,
	onProjectUpdated,
}) => {
	const {
		isSubmitting,
		error,
		selectedMembers,
		setSelectedMembers,
		handleSubmit,
		clearError,
	} = useProjectForm({
		mode,
		project,
		isOpen,
		onSuccess: onClose,
		onProjectCreated,
		onProjectUpdated,
	});

	const getDialogContent = () => {
		if (mode === 'create') {
			return {
				title: 'Create New Project',
				description: 'Create a new project to organize your creative work.',
				submitText: 'Create Project',
				submittingText: 'Creating...',
			};
		}
		return {
			title: 'Edit Project',
			description: 'Update your project details.',
			submitText: 'Update Project',
			submittingText: 'Updating...',
		};
	};

	const dialogContent = getDialogContent();

	const handleClose = () => {
		if (!isSubmitting) {
			clearError();
			onClose();
		}
	};

	return (
		<Dialog
			isOpen={isOpen}
			onClose={handleClose}
			title={dialogContent.title}
			description={dialogContent.description}
			size='lg'>
			{error && (
				<Alert
					variant='negative'
					className='mb-3'>
					{error}
				</Alert>
			)}

			<form
				onSubmit={handleSubmit}
				className='space-y-3'>
				<Fieldset className='space-y-3'>
					<Field>
						<Label htmlFor='name'>
							<Typography
								variant='body-sm'
								weight='medium'
								color='secondary'>
								Project Name
							</Typography>
						</Label>
						<Input
							id='name'
							name='name'
							type='text'
							required
							placeholder='Enter project name'
							defaultValue={mode === 'edit' ? project?.name || '' : ''}
							className='mt-1'
							disabled={isSubmitting}
						/>
					</Field>

					<Field>
						<Label htmlFor='description'>
							<Typography
								variant='body-sm'
								weight='medium'
								color='secondary'>
								Description
							</Typography>
						</Label>
						<Textarea
							id='description'
							name='description'
							rows={4}
							required
							placeholder='Describe your project...'
							defaultValue={mode === 'edit' ? project?.description || '' : ''}
							disabled={isSubmitting}
							className='mt-1'
						/>
					</Field>

					<MemberSelect
						value={selectedMembers}
						onChange={setSelectedMembers}
						disabled={isSubmitting}
						className='mt-1'
					/>
				</Fieldset>

				<div className='flex flex-col sm:flex-row justify-end gap-1.5 pt-2'>
					<Button
						type='button'
						variant='secondary'
						onClick={handleClose}
						disabled={isSubmitting}
						className='order-2 sm:order-1'>
						Cancel
					</Button>
					<Button
						type='submit'
						variant='primary'
						disabled={isSubmitting}
						className='order-1 sm:order-2'>
						{isSubmitting && <ButtonLoader />}
						{isSubmitting
							? dialogContent.submittingText
							: dialogContent.submitText}
					</Button>
				</div>
			</form>
		</Dialog>
	);
};

export default ProjectFormDialog;
export type { ProjectFormDialogProps };
