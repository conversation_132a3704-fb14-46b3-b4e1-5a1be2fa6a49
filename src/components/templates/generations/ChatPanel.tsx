import React, { useState, useEffect, useRef } from 'react';
import { SparklesIcon, UserIcon, TagIcon } from '@heroicons/react/24/outline';
import { Typography } from '@/components/atoms';
import { PromptInput } from '@/components/molecules';
import { Button } from '@/components/atoms';
import { Badge } from '@/components/atoms';
import { GenerationResponse, ConversationMessage } from '@/api/GenerationApi';

interface ChatPanelProps {
	generation: GenerationResponse;
	messages: ConversationMessage[];
	onSendMessage: (message: string) => Promise<void>;
	onRegenerateCode: (prompt: string) => void;
	isGenerating: boolean;
}

export const ChatPanel: React.FC<ChatPanelProps> = ({
	generation,
	messages,
	onSendMessage,
	isGenerating,
}) => {
	const [newMessage, setNewMessage] = useState('');
	const messagesEndRef = useRef<HTMLDivElement>(null);

	const scrollToBottom = () => {
		messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
	};

	useEffect(() => {
		scrollToBottom();
	}, [messages]);

	const handleSendMessage = async (message: string) => {
		if (!message.trim() || isGenerating) return;

		setNewMessage('');
		try {
			await onSendMessage(message);
		} catch (error) {
			console.error('Error sending message:', error);
		}
	};

	const formatTime = (date: Date) => {
		return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
	};

	return (
		<div className='flex flex-col h-full bg-surface-50'>
			{/* Header */}
			<div className='p-4 border-b border-border-secondary bg-surface-100'>
				<div className='flex items-center gap-3'>
					<div className='p-2 bg-primary/10 rounded-lg'>
						<SparklesIcon className='w-5 h-5 text-primary' />
					</div>
					<div>
						<Typography
							variant='h4'
							weight='semibold'>
							{generation.type === 'UI' ? 'UI Generation' : 'Documentation'}
						</Typography>
						<Typography
							variant='body-sm'
							color='secondary'>
							{generation.project?.name}
						</Typography>
					</div>
				</div>

				{/* Generation tags */}
				{generation.promptHistory &&
					generation.promptHistory.tags.length > 0 && (
						<div className='flex flex-wrap gap-1 mt-3'>
							{generation.promptHistory.tags.map(
								(tag: string, index: number) => (
									<Badge
										key={index}
										variant='secondary'
										size='small'
										className='text-xs'>
										<TagIcon className='w-3 h-3 mr-1' />
										{tag}
									</Badge>
								),
							)}
						</div>
					)}
			</div>

			{/* Messages */}
			<div className='flex-1 overflow-y-auto overflow-x-hidden p-4 space-y-3'>
				{/* Initial prompt display */}
				{generation.prompt && (
					<div className='flex justify-start mb-6'>
						<div className='flex-shrink-0 w-8 h-8 bg-accent-purple-100 rounded-full flex items-center justify-center mr-3'>
							<SparklesIcon className='w-4 h-4 text-accent-purple-600' />
						</div>
						<div className='max-w-sm lg:max-w-2xl px-4 py-3 rounded-lg bg-accent-purple-50 border border-accent-purple-200'>
							<Typography
								variant='caption'
								color='secondary'
								className='uppercase tracking-wide text-xs font-medium mb-1'>
								Initial Prompt
							</Typography>
							<Typography
								variant='body-sm'
								className='text-accent-purple-800'>
								{generation.prompt}
							</Typography>
						</div>
					</div>
				)}

				{messages.map((message) => (
					<div
						key={message.id}
						className={`flex ${
							message.role === 'USER' ? 'justify-end' : 'justify-start'
						}`}>
						{message.role === 'ASSISTANT' && (
							<div className='flex-shrink-0 w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center mr-3 mt-1'>
								<SparklesIcon className='w-4 h-4 text-primary' />
							</div>
						)}
						<div
							className={`${
								message.role === 'USER'
									? 'max-w-xs lg:max-w-md'
									: 'max-w-sm lg:max-w-2xl'
							} px-4 py-3 rounded-2xl ${
								message.role === 'USER'
									? 'bg-primary text-white rounded-br-md'
									: 'bg-surface-100 text-foreground rounded-bl-md border border-border-secondary'
							}`}>
							<Typography
								variant='body-sm'
								className={`whitespace-pre-wrap leading-relaxed break-words ${
									message.role === 'USER' ? 'text-white' : ''
								}`}>
								{message.content}
							</Typography>
							<Typography
								variant='caption'
								className={`block mt-2 text-xs ${
									message.role === 'USER' ? 'text-white/70' : 'text-muted'
								}`}>
								{formatTime(new Date(message.createdAt))}
							</Typography>
						</div>
						{message.role === 'USER' && (
							<div className='flex-shrink-0 w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center ml-3 mt-1'>
								<UserIcon className='w-4 h-4 text-primary' />
							</div>
						)}
					</div>
				))}

				{isGenerating && (
					<div className='flex justify-start'>
						<div className='flex-shrink-0 w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center mr-3 mt-1'>
							<SparklesIcon className='w-4 h-4 text-primary animate-pulse' />
						</div>
						<div className='max-w-xs lg:max-w-md px-4 py-3 rounded-2xl rounded-bl-md bg-surface-100 text-foreground border border-border-secondary'>
							<div className='flex items-center gap-2'>
								<div className='w-2 h-2 bg-primary rounded-full animate-bounce' />
								<div
									className='w-2 h-2 bg-primary rounded-full animate-bounce'
									style={{ animationDelay: '0.1s' }}
								/>
								<div
									className='w-2 h-2 bg-primary rounded-full animate-bounce'
									style={{ animationDelay: '0.2s' }}
								/>
								<Typography
									variant='body-sm'
									color='secondary'
									className='ml-1'>
									AI is thinking...
								</Typography>
							</div>
						</div>
					</div>
				)}

				<div ref={messagesEndRef} />
			</div>

			{/* Input */}
			<div className='p-4 border-t border-border-secondary bg-surface-100'>
				{/* Chat-style input */}
				<PromptInput
					value={newMessage}
					onChange={setNewMessage}
					onSubmit={() => handleSendMessage(newMessage)}
					placeholder='Ask a question or request changes...'
					disabled={isGenerating}
					loading={isGenerating}
					variant='compact'
					size='small'
					submitButtonText='Send'
					showCharacterCount={false}
					className='bg-surface-50 border border-border-secondary shadow-none rounded-xl'
					textareaClassName='bg-transparent border-none shadow-none focus:ring-1 focus:ring-primary/20 placeholder:text-muted'
					buttonClassName='bg-primary hover:bg-primary/90 text-white shadow-sm'
					rows={2}
				/>

				{/* Input hints */}
				<div className='flex items-center justify-between mt-2 px-2'>
					<Typography
						variant='caption'
						color='tertiary'
						className='text-xs'>
						Press Ctrl+Enter to send • Be specific for better results
					</Typography>
					{newMessage.trim() && (
						<Button
							variant='ghost'
							size='sm'
							onClick={() => setNewMessage('')}
							disabled={isGenerating}
							className='text-xs px-2 py-1 h-auto'>
							Clear
						</Button>
					)}
				</div>
			</div>
		</div>
	);
};
