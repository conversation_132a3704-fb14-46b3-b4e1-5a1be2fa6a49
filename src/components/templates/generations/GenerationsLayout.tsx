import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/atoms';
import {
	ChevronLeftIcon,
	ChevronRightIcon,
	ChatBubbleLeftRightIcon,
	EyeIcon,
	ArrowsPointingOutIcon,
	ArrowsPointingInIcon,
} from '@heroicons/react/24/outline';

interface GenerationsLayoutProps {
	chatPanel: React.ReactNode;
	codePreviewPanel: React.ReactNode;
}

export const GenerationsLayout: React.FC<GenerationsLayoutProps> = ({
	chatPanel,
	codePreviewPanel,
}) => {
	const [isChatPanelCollapsed, setIsChatPanelCollapsed] = useState(false);
	const [isMobile, setIsMobile] = useState(false);
	const [mobileView, setMobileView] = useState<'chat' | 'preview'>('chat');
	const [isFullscreen, setIsFullscreen] = useState(false);
	const layoutRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		const mediaQuery = window.matchMedia('(max-width: 768px)');
		const handleResize = (e: MediaQueryListEvent | { matches: boolean }) => {
			setIsMobile(e.matches);
			if (e.matches) {
				setIsChatPanelCollapsed(true);
			} else {
				setIsChatPanelCollapsed(false);
			}
		};

		// Initial check
		handleResize({ matches: mediaQuery.matches });

		// Use addEventListener for modern browsers
		mediaQuery.addEventListener('change', handleResize);

		return () => {
			mediaQuery.removeEventListener('change', handleResize);
		};
	}, []);

	const toggleChatPanel = () => {
		setIsChatPanelCollapsed(!isChatPanelCollapsed);
	};

	const toggleFullscreen = () => {
		if (!document.fullscreenElement) {
			layoutRef.current?.requestFullscreen().catch((err) => {
				console.error(`Error attempting to enable fullscreen: ${err.message}`);
			});
			setIsFullscreen(true);
		} else {
			document.exitFullscreen();
			setIsFullscreen(false);
		}
	};

	useEffect(() => {
		const handleFullscreenChange = () => {
			setIsFullscreen(!!document.fullscreenElement);
		};

		document.addEventListener('fullscreenchange', handleFullscreenChange);
		return () => {
			document.removeEventListener('fullscreenchange', handleFullscreenChange);
		};
	}, []);

	if (isMobile) {
		return (
			<div
				className='flex flex-col h-full'
				ref={layoutRef}>
				{/* Mobile Tabs */}
				<div className='flex-shrink-0 border-b border-border-secondary p-3 flex items-center justify-between gap-2 bg-surface-50'>
					<div className='flex flex-1 gap-2'>
						<Button
							variant={mobileView === 'chat' ? 'primary' : 'ghost'}
							onClick={() => setMobileView('chat')}
							size='sm'
							className='flex-1 justify-center'
							aria-label='Chat'>
							<ChatBubbleLeftRightIcon className='w-4 h-4 mr-1.5' />
							Chat
						</Button>
						<Button
							variant={mobileView === 'preview' ? 'primary' : 'ghost'}
							onClick={() => setMobileView('preview')}
							size='sm'
							className='flex-1 justify-center'
							aria-label='Preview'>
							<EyeIcon className='w-4 h-4 mr-1.5' />
							Preview
						</Button>
					</div>
					<Button
						variant='ghost'
						size='sm'
						onClick={toggleFullscreen}
						className='p-2'
						aria-label={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}>
						{isFullscreen ? (
							<ArrowsPointingInIcon className='w-4 h-4' />
						) : (
							<ArrowsPointingOutIcon className='w-4 h-4' />
						)}
					</Button>
				</div>

				{/* Mobile Content */}
				<div className='flex-1 overflow-auto'>
					{mobileView === 'chat' && <div className='h-full'>{chatPanel}</div>}
					{mobileView === 'preview' && (
						<div className='h-full'>{codePreviewPanel}</div>
					)}
				</div>
			</div>
		);
	}

	return (
		<div
			className='flex h-full w-full'
			ref={layoutRef}>
			{/* Chat Panel */}
			<div
				className={`
          flex-shrink-0 bg-surface-50 border-r border-border-secondary transition-all duration-300 ease-in-out relative
          ${isChatPanelCollapsed ? 'w-0' : 'w-80 lg:w-96 xl:w-[28rem]'}
        `}>
				<div
					className={`h-full overflow-hidden ${
						isChatPanelCollapsed ? 'opacity-0 invisible' : 'opacity-100 visible'
					} transition-opacity duration-300`}>
					{chatPanel}
				</div>
			</div>

			{/* Main Content: Code & Preview */}
			<div className='flex-1 flex flex-col bg-surface-100 relative'>
				{/* Toggle Button */}
				<Button
					variant='ghost'
					size='sm'
					onClick={toggleChatPanel}
					className='absolute top-1/2 -translate-y-1/2 bg-surface-50 border border-border-secondary rounded-full p-1.5 z-10 transition-all duration-300 -ml-4 hover:bg-surface-100 shadow-sm'
					style={{ left: isChatPanelCollapsed ? '0.75rem' : '0' }}
					aria-label={
						isChatPanelCollapsed ? 'Expand chat panel' : 'Collapse chat panel'
					}>
					{isChatPanelCollapsed ? (
						<ChevronRightIcon className='w-4 h-4' />
					) : (
						<ChevronLeftIcon className='w-4 h-4' />
					)}
				</Button>

				{/* Fullscreen Button */}
				<Button
					variant='ghost'
					size='sm'
					onClick={toggleFullscreen}
					className='absolute top-4 right-4 bg-surface-50 border border-border-secondary rounded-full p-1.5 z-10 hover:bg-surface-100 shadow-sm'
					aria-label={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}>
					{isFullscreen ? (
						<ArrowsPointingInIcon className='w-4 h-4' />
					) : (
						<ArrowsPointingOutIcon className='w-4 h-4' />
					)}
				</Button>

				{/* Content */}
				<div className='flex-1 overflow-hidden'>{codePreviewPanel}</div>
			</div>
		</div>
	);
};
