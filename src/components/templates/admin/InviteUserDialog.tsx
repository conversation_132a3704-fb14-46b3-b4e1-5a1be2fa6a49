import { useState } from 'react';
import { Field, Label, Fieldset } from '@headlessui/react';
import {
	Input,
	<PERSON><PERSON>,
	Alert,
	<PERSON>ton<PERSON>oader,
	Typography,
	Select,
} from '@/components/atoms';
import { Dialog } from '@/components/molecules';
import type { SelectOption } from '@/components/atoms/Select';

interface InviteUserDialogProps {
	isOpen: boolean;
	onClose: () => void;
	onInvite: (email: string) => Promise<void>;
}

const roleOptions: SelectOption[] = [
	{
		id: 'USER',
		value: 'USER',
		label: 'User',
		description: 'Regular user with standard permissions',
	},
	{
		id: 'ADMIN',
		value: 'ADMIN',
		label: 'Admin',
		description: 'Administrator with full system access',
	},
];

export const InviteUserDialog: React.FC<InviteUserDialogProps> = ({
	isOpen,
	onClose,
	onInvite,
}) => {
	const [email, setEmail] = useState('');
	const [selectedRole, setSelectedRole] = useState<SelectOption>(
		roleOptions[0],
	);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const handleSubmit = async (event: React.FormEvent) => {
		event.preventDefault();
		setError(null);

		if (!email.trim()) {
			setError('Email is required');
			return;
		}

		if (!email.includes('@')) {
			setError('Please enter a valid email address');
			return;
		}

		setIsSubmitting(true);

		try {
			await onInvite(email.trim());
			// Reset form on success
			setEmail('');
			setSelectedRole(roleOptions[0]);
		} catch (err) {
			setError(
				err instanceof Error ? err.message : 'Failed to send invitation',
			);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleClose = () => {
		if (!isSubmitting) {
			setEmail('');
			setSelectedRole(roleOptions[0]);
			setError(null);
			onClose();
		}
	};

	return (
		<Dialog
			isOpen={isOpen}
			onClose={handleClose}
			title='Invite New User'
			description='Send an invitation to a new user to join the platform.'>
			<form
				onSubmit={handleSubmit}
				className='space-y-4'>
				{error && (
					<Alert
						variant='negative'
						className='mb-4'>
						{error}
					</Alert>
				)}

				<Fieldset className='space-y-4'>
					<Field>
						<Label>
							<Typography
								variant='body-sm'
								weight='medium'
								color='secondary'
								className='mb-2'>
								Email Address
							</Typography>
						</Label>
						<Input
							type='email'
							value={email}
							onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
								setEmail(e.target.value)
							}
							placeholder='<EMAIL>'
							required
							disabled={isSubmitting}
							className='w-full'
						/>
					</Field>

					<Field>
						<Label>
							<Typography
								variant='body-sm'
								weight='medium'
								color='secondary'
								className='mb-2'>
								Role
							</Typography>
						</Label>
						<Select
							options={roleOptions}
							value={selectedRole}
							onChange={(option) => {
								if (option && !Array.isArray(option)) {
									setSelectedRole(option);
								}
							}}
							placeholder='Select a role'
							disabled={true}
							size='md'
						/>
						<Typography
							variant='caption'
							color='tertiary'
							className='mt-1'>
							Note: New users will be assigned the USER role by default. You can
							change their role after they accept the invitation.
						</Typography>
					</Field>
				</Fieldset>

				<div className='flex justify-end gap-3 pt-4'>
					<Button
						type='button'
						variant='outline'
						size='md'
						onClick={handleClose}
						disabled={isSubmitting}>
						Cancel
					</Button>
					<Button
						type='submit'
						variant='primary'
						size='md'
						disabled={isSubmitting || !email.trim()}>
						{isSubmitting && <ButtonLoader />}
						{isSubmitting ? 'Sending...' : 'Send Invitation'}
					</Button>
				</div>
			</form>
		</Dialog>
	);
};
