import { Fragment } from 'react';
import { clsx } from 'clsx';
import SidebarSection, {
	SidebarSectionProps,
} from '../molecules/SidebarSection';

import { SidebarHeader } from '../molecules';
import { Divider } from '../atoms';

interface SidebarProps {
	title: string;
	subtitle?: string;
	sections: SidebarSectionProps[];
	isCollapsed?: boolean;
	onToggle?: () => void;
	onItemClick?: (itemId: string) => void;
	className?: string;
}

const Sidebar: React.FC<SidebarProps> = ({
	title,
	subtitle,
	sections,
	isCollapsed = false,
	onToggle,
	className,
}) => {
	return (
		<aside
			className={clsx(
				'flex flex-col h-full',
				'bg-layout-50 border-r border-border-secondary lg:border-r',
				'shadow-sm lg:shadow-none',
				'transition-all duration-200 ease-in-out',
				isCollapsed ? 'w-20' : 'w-64 sm:w-72 lg:w-64',
				'relative lg:static z-50 lg:z-auto',
				className,
			)}
			role='navigation'
			aria-label='Main navigation'>
			{onToggle && (
				<SidebarHeader
					title={title}
					subtitle={subtitle}
					isCollapsed={isCollapsed}
					onToggle={onToggle}
				/>
			)}

			<div className='flex-1 overflow-y-auto bg-layout-100 scrollbar-thin scrollbar-track-transparent scrollbar-thumb-border-border-secondary'>
				{sections.map((section, index) => (
					<Fragment key={section.title || index}>
						{index > 0 && <Divider className='mx-3 my-2 border-border-muted' />}
						<SidebarSection
							title={section.title || ''}
							isCollapsed={isCollapsed}
							items={section.items}
						/>
					</Fragment>
				))}
			</div>
		</aside>
	);
};

Sidebar.displayName = 'Sidebar';
export default Sidebar;
