import { useState } from 'react';
import Dialog from '@/components/molecules/Dialog';
import { Button, Input, Textarea, Typography, Alert } from '@/components/atoms';
import { useGenerations } from '@/hooks/useGeneration';
import { useProjects } from '@/hooks/useProjects';

interface CreateGenerationDialogProps {
	isOpen: boolean;
	onClose: () => void;
	onSuccess?: (generationId: string) => void;
	projectId?: string;
}

export const CreateGenerationDialog: React.FC<CreateGenerationDialogProps> = ({
	isOpen,
	onClose,
	onSuccess,
	projectId,
}) => {
	const [name, setName] = useState('');
	const [type, setType] = useState<'UI' | 'DOCUMENTATION'>('UI');
	const [prompt, setPrompt] = useState('');
	const [selectedProjectId, setSelectedProjectId] = useState(projectId || '');
	const [error, setError] = useState<string | null>(null);

	const { createGeneration, isLoading } = useGenerations();
	const { projects } = useProjects();

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setError(null);

		if (!name.trim()) {
			setError('Please enter a name for the generation');
			return;
		}

		if (!prompt.trim()) {
			setError('Please enter a prompt');
			return;
		}

		if (!selectedProjectId) {
			setError('Please select a project');
			return;
		}

		const result = await createGeneration({
			name: name.trim(),
			type,
			initialPrompt: prompt.trim(),
			projectId: selectedProjectId,
		});

		if (result) {
			onSuccess?.(result.id);
			handleClose();
		}
	};

	const handleClose = () => {
		setName('');
		setType('UI');
		setPrompt('');
		setSelectedProjectId(projectId || '');
		setError(null);
		onClose();
	};

	return (
		<Dialog
			isOpen={isOpen}
			onClose={handleClose}
			size='lg'
			title='Create New Generation'>
			<form
				onSubmit={handleSubmit}
				className='space-y-4'>
				{error && <Alert variant='negative'>{error}</Alert>}

				<div>
					<label
						htmlFor='name'
						className='block text-sm font-medium text-foreground mb-1'>
						Name
					</label>
					<Input
						id='name'
						type='text'
						value={name}
						onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
							setName(e.target.value)
						}
						placeholder='e.g., Dashboard Component'
						maxLength={100}
						required
					/>
					<Typography
						variant='caption'
						color='secondary'
						className='mt-1'>
						{name.length}/100 characters
					</Typography>
				</div>

				<div>
					<label
						htmlFor='type'
						className='block text-sm font-medium text-foreground mb-1'>
						Type
					</label>
					<select
						id='type'
						value={type}
						onChange={(e) => setType(e.target.value as 'UI' | 'DOCUMENTATION')}
						className='w-full px-3 py-2 border border-border rounded-md focus:ring-2 focus:ring-primary focus:border-primary'
						required>
						<option value='UI'>UI Generation</option>
						<option value='DOCUMENTATION'>Documentation Generation</option>
					</select>
				</div>

				{!projectId && (
					<div>
						<label
							htmlFor='project'
							className='block text-sm font-medium text-foreground mb-1'>
							Project
						</label>
						<select
							id='project'
							value={selectedProjectId}
							onChange={(e) => setSelectedProjectId(e.target.value)}
							className='w-full px-3 py-2 border border-border rounded-md focus:ring-2 focus:ring-primary focus:border-primary'
							required>
							<option value=''>Select a project</option>
							{projects.map((project) => (
								<option
									key={project.id}
									value={project.id}>
									{project.name}
								</option>
							))}
						</select>
					</div>
				)}

				<div>
					<label
						htmlFor='prompt'
						className='block text-sm font-medium text-foreground mb-1'>
						Initial Prompt
					</label>
					<Textarea
						id='prompt'
						value={prompt}
						onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
							setPrompt(e.target.value)
						}
						placeholder={
							type === 'UI'
								? 'Describe the UI component you want to create...'
								: 'Describe the documentation you want to generate...'
						}
						rows={4}
						maxLength={2000}
						required
					/>
					<Typography
						variant='caption'
						color='secondary'
						className='mt-1'>
						{prompt.length}/2000 characters
					</Typography>
				</div>

				<div className='flex justify-end gap-3 pt-4'>
					<Button
						type='button'
						variant='secondary'
						onClick={handleClose}
						disabled={isLoading}>
						Cancel
					</Button>
					<Button
						type='submit'
						variant='primary'
						disabled={isLoading}>
						{isLoading ? 'Creating...' : 'Create Generation'}
					</Button>
				</div>
			</form>
		</Dialog>
	);
};
