import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>,
	CardContent,
	Alert,
} from '@/components/atoms';
import { CreateGenerationDialog } from '@/components/organisms/CreateGenerationDialog';
import { useGenerations } from '@/hooks/useGeneration';
import { generationService } from '@/services/generationService';
import {
	PlusIcon,
	SparklesIcon,
	ClockIcon,
	FolderIcon,
	CodeBracketIcon,
	DocumentTextIcon,
	EyeIcon,
	ArrowTopRightOnSquareIcon,
} from '@heroicons/react/24/outline';
import { formatDistanceToNow } from 'date-fns';

// Types based on API response
interface GenerationListItem {
	id: string;
	name: string;
	type: 'UI' | 'DOCUMENTATION';
	prompt: string;
	initialPrompt: string;
	status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
	result: string | null;
	currentResult: string | null;
	metadata: {
		model?: string;
		processingTime?: number;
		tokensGenerated?: number;
		framework?: string;
		styling?: string;
		language?: string;
		error?: boolean;
		errorMessage?: string;
	} | null;
	createdAt: string;
	updatedAt: string;
	project: {
		id: string;
		name: string;
		description?: string;
		status: 'ACTIVE' | 'INACTIVE' | 'ARCHIVED';
	};
	createdBy: {
		id: string;
		email: string;
		fullName?: string;
	};
	promptHistory?: {
		id: string;
		prompt: string;
		description?: string;
		tags: string[];
		usageCount: number;
	};
}

interface GenerationsListProps {
	className?: string;
	showCreateButton?: boolean;
	maxItems?: number;
	projectId?: string;
}

export const GenerationsList: React.FC<GenerationsListProps> = ({
	className = '',
	showCreateButton = true,
	maxItems,
	projectId,
}) => {
	const router = useRouter();
	const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
	const { generations, isLoading, error, fetchGenerations } = useGenerations();

	useEffect(() => {
		fetchGenerations();
	}, [fetchGenerations]);

	const getStatusBadge = (status: GenerationListItem['status']) => {
		return generationService.getStatusBadge(status);
	};

	const handleViewGeneration = (id: string) => {
		router.push(`/generations/${id}`);
	};

	const handleCreateNew = () => {
		setIsCreateDialogOpen(true);
	};

	const handleCreateSuccess = (generationId: string) => {
		router.push(`/generations/${generationId}`);
	};

	// Filter generations by project if specified
	const filteredGenerations = projectId
		? generations.filter((gen) => gen.project.id === projectId)
		: generations;

	// Limit items if specified
	const displayGenerations = maxItems
		? filteredGenerations.slice(0, maxItems)
		: filteredGenerations;

	if (isLoading) {
		return (
			<div className={`flex items-center justify-center py-12 ${className}`}>
				<div className='text-center'>
					<div className='animate-spin rounded-full h-8 w-8 border-2 border-border-secondary border-t-primary mx-auto mb-4'></div>
					<Typography
						variant='body-sm'
						color='secondary'>
						Loading generations...
					</Typography>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className={`p-4 ${className}`}>
				<Alert
					variant='negative'
					className='mb-4'>
					{error}
				</Alert>
			</div>
		);
	}

	if (displayGenerations.length === 0) {
		return (
			<div className={`text-center py-12 px-4 ${className}`}>
				<div className='max-w-md mx-auto'>
					{/* Icon */}
					<div className='relative mb-6'>
						<div className='w-16 h-16 bg-gradient-to-br from-primary/10 to-primary/5 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg shadow-primary/5'>
							<SparklesIcon className='w-8 h-8 text-primary' />
						</div>
						<div className='absolute -top-1 -right-1 w-5 h-5 bg-accent-purple-100 rounded-full flex items-center justify-center'>
							<PlusIcon className='w-2.5 h-2.5 text-accent-purple-600' />
						</div>
					</div>

					{/* Content */}
					<Typography
						variant='h3'
						weight='bold'
						className='mb-2'>
						{projectId ? 'No Generations Yet' : 'Start Creating'}
					</Typography>
					<Typography
						variant='body'
						color='secondary'
						className='mb-6'>
						{projectId
							? "This project doesn't have any generations yet."
							: 'Transform your ideas into reality with AI-powered generation.'}
					</Typography>

					{/* Action */}
					{showCreateButton && (
						<Button
							variant='primary'
							size='md'
							onClick={handleCreateNew}
							className='flex items-center gap-2 mx-auto'>
							<PlusIcon className='w-4 h-4' />
							Create Generation
						</Button>
					)}
				</div>
			</div>
		);
	}

	return (
		<div className={className}>
			{/* Header */}
			{showCreateButton && !projectId && (
				<div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6'>
					<div className='flex-1 min-w-0'>
						<Typography
							variant='h2'
							weight='bold'
							className='mb-1'>
							Recent Generations
						</Typography>
						<Typography
							variant='body'
							color='secondary'>
							{displayGenerations.length} of {filteredGenerations.length}{' '}
							generations
						</Typography>
					</div>
					<div className='flex-shrink-0'>
						<Button
							variant='primary'
							size='sm'
							onClick={handleCreateNew}
							className='flex items-center gap-2 w-full sm:w-auto'>
							<PlusIcon className='w-4 h-4' />
							New Generation
						</Button>
					</div>
				</div>
			)}

			{/* Generations Grid */}
			<div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-4 sm:gap-6'>
				{displayGenerations.map((generation) => {
					const statusBadge = getStatusBadge(generation.status);
					return (
						<Card
							key={generation.id}
							variant='default'
							size='medium'
							className='group hover:shadow-lg hover:shadow-primary/5 hover:-translate-y-0.5 transition-all duration-200 cursor-pointer border-border-secondary hover:border-primary/20 bg-surface-50 hover:bg-surface-100 h-full'>
							<CardContent className='p-4 sm:p-5 lg:p-6 h-full flex flex-col'>
								{/* Header */}
								<div className='flex items-start justify-between mb-4 gap-3'>
									<div className='flex items-center gap-3 min-w-0 flex-1'>
										<div
											className={`p-2 rounded-lg flex-shrink-0 ${
												generation.type === 'UI'
													? 'bg-primary/10'
													: 'bg-accent-purple-100'
											}`}>
											{generation.type === 'UI' ? (
												<CodeBracketIcon className='w-4 h-4 text-primary' />
											) : (
												<DocumentTextIcon className='w-4 h-4 text-accent-purple-600' />
											)}
										</div>
										<Typography
											variant='body-sm'
											weight='semibold'
											color='primary'
											className='uppercase tracking-wide text-xs sm:text-sm truncate'>
											{generation.type}
										</Typography>
									</div>
									<div className='flex-shrink-0'>
										<Badge
											variant={statusBadge.variant}
											size='medium'
											emphasis='light'>
											{statusBadge.text}
										</Badge>
									</div>
								</div>

								{/* Name */}
								<Typography
									variant='h6'
									weight='semibold'
									className='mb-3 line-clamp-2 group-hover:text-primary transition-colors duration-200 text-base sm:text-lg'>
									{generation.name || 'Untitled Generation'}
								</Typography>

								{/* Prompt */}
								<Typography
									variant='body'
									color='secondary'
									className='mb-4 line-clamp-3 leading-relaxed text-sm sm:text-base'>
									{generation.prompt}
								</Typography>

								{/* Tags */}
								{generation.promptHistory &&
									generation.promptHistory.tags.length > 0 && (
										<div className='flex flex-wrap gap-1 mb-3'>
											{generation.promptHistory.tags.slice(0, 2).map((tag) => (
												<Badge
													key={tag}
													variant='secondary'
													size='small'
													emphasis='light'
													className='text-xs'>
													{tag}
												</Badge>
											))}
											{generation.promptHistory.tags.length > 2 && (
												<Badge
													variant='secondary'
													size='small'
													emphasis='light'
													className='text-xs'>
													+{generation.promptHistory.tags.length - 2}
												</Badge>
											)}
										</div>
									)}

								{/* Footer */}
								<div className='pt-4 border-t border-border-secondary/50 mt-auto'>
									<div className='flex items-center justify-between mb-3'>
										<div className='flex items-center gap-4 min-w-0 flex-1'>
											{!projectId && (
												<div className='flex items-center gap-2 min-w-0'>
													<FolderIcon className='w-4 h-4 text-muted flex-shrink-0' />
													<Typography
														variant='body-sm'
														color='secondary'
														className='truncate'>
														{generation.project.name}
													</Typography>
												</div>
											)}
											<div className='flex items-center gap-2 min-w-0'>
												<ClockIcon className='w-4 h-4 text-muted flex-shrink-0' />
												<Typography
													variant='body-sm'
													color='tertiary'
													className='truncate'>
													{formatDistanceToNow(new Date(generation.createdAt), {
														addSuffix: true,
													})}
												</Typography>
											</div>
										</div>
									</div>

									{/* Actions */}
									<div className='flex items-center gap-2 sm:gap-3'>
										<Button
											variant='ghost'
											size='md'
											onClick={(e) => {
												e.stopPropagation();
												handleViewGeneration(generation.id);
											}}
											className='flex items-center gap-2 text-sm px-3 py-2 flex-1 min-w-0'>
											<EyeIcon className='w-4 h-4 flex-shrink-0' />
											<span className='truncate'>View</span>
										</Button>
										{generation.status === 'COMPLETED' && (
											<Button
												variant='ghost'
												size='md'
												onClick={(e) => {
													e.stopPropagation();
													handleViewGeneration(generation.id);
												}}
												className='flex items-center gap-2 text-sm px-3 py-2 flex-shrink-0'>
												<ArrowTopRightOnSquareIcon className='w-4 h-4' />
												<span className='hidden sm:inline'>Open</span>
											</Button>
										)}
									</div>
								</div>
							</CardContent>
						</Card>
					);
				})}
			</div>

			{/* Show more link */}
			{maxItems && filteredGenerations.length > maxItems && (
				<div className='text-center mt-6'>
					<Button
						variant='ghost'
						size='sm'
						onClick={() => router.push('/generations')}
						className='flex items-center gap-2 mx-auto'>
						View All Generations
						<ArrowTopRightOnSquareIcon className='w-4 h-4' />
					</Button>
				</div>
			)}

			{/* Create Generation Dialog */}
			<CreateGenerationDialog
				isOpen={isCreateDialogOpen}
				onClose={() => setIsCreateDialogOpen(false)}
				onSuccess={handleCreateSuccess}
			/>
		</div>
	);
};
