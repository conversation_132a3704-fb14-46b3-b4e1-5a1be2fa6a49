import { useState, useCallback } from 'react';
import { useRouter } from 'next/router';

import AppTemplate from '@/components/templates/AppTemplate';
import {
	ProjectsHeader,
	ProjectsGrid,
	ProjectsSidebar,
} from '@/components/templates';
import { NextPageWithLayout } from '@/pages/_app';
import { withAuth } from '@/components/guards';

import { useProjects } from '@/hooks/useProjects';
import type { Project } from '@/components/templates/project/ProjectsGrid';

import ProjectFormDialog from '@/components/templates/project/ProjectFormDialog';
import DeleteProjectDialog from '@/components/templates/project/DeleteProjectDialog';
import { Typography } from '@/components/atoms';

const ProjectsPage: NextPageWithLayout = () => {
	const router = useRouter();
	const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
	const [isProjectDialogOpen, setIsProjectDialogOpen] = useState(false);
	const [projectDialogMode, setProjectDialogMode] = useState<'create' | 'edit'>(
		'create',
	);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [selectedProject, setSelectedProject] = useState<Project | null>(null);

	const {
		projects,
		createProject,
		updateProject,
		deleteProject,
		isLoading,
		canManageProjects,
	} = useProjects();

	const handleOpenCreateDialog = useCallback(() => {
		setProjectDialogMode('create');
		setSelectedProject(null);
		setIsProjectDialogOpen(true);
	}, []);

	const handleCloseProjectDialog = useCallback(() => {
		setIsProjectDialogOpen(false);
		setSelectedProject(null);
	}, []);

	const handleToggleSidebar = useCallback(() => {
		setIsSidebarCollapsed(!isSidebarCollapsed);
	}, [isSidebarCollapsed]);

	const handleProjectCreated = useCallback(
		async (projectData: { name: string; description: string }) => {
			const newProject = await createProject(projectData);
			if (newProject) {
				setIsProjectDialogOpen(false);
			}
			return newProject;
		},
		[createProject],
	);

	const handleEditProject = useCallback((project: Project) => {
		setSelectedProject(project);
		setProjectDialogMode('edit');
		setIsProjectDialogOpen(true);
	}, []);

	const handleDeleteProject = useCallback((project: Project) => {
		setSelectedProject(project);
		setIsDeleteDialogOpen(true);
	}, []);

	const handleProjectUpdated = useCallback(
		async (
			id: string,
			projectData: {
				name: string;
				description: string;
			},
		) => {
			const updatedProject = await updateProject(id, projectData);
			if (updatedProject) {
				setIsProjectDialogOpen(false);
				setSelectedProject(null);
			}
			return updatedProject;
		},
		[updateProject],
	);

	const handleProjectDeleted = useCallback(
		async (id: string) => {
			const success = await deleteProject(id);
			if (success) {
				setIsDeleteDialogOpen(false);
				setSelectedProject(null);
			}
			return success;
		},
		[deleteProject],
	);

	const handleCloseDeleteDialog = useCallback(() => {
		setIsDeleteDialogOpen(false);
		setSelectedProject(null);
	}, []);

	const handleProjectClick = useCallback(
		(project: Project) => {
			router.push(`/projects/${project.id}`);
		},
		[router],
	);
	return (
		<div className='flex h-svh bg-layout-50'>
			{/* Sidebar - hidden on mobile, overlay on tablet, fixed on desktop */}
			<div
				className={`
				${
					isSidebarCollapsed
						? 'hidden lg:block'
						: 'fixed lg:relative inset-0 lg:inset-auto z-50 lg:z-auto'
				}
				${!isSidebarCollapsed ? 'lg:block' : ''}
			`}>
				{!isSidebarCollapsed && (
					<div
						className='absolute inset-0 bg-neutral-900/40 backdrop-blur-sm lg:hidden'
						onClick={handleToggleSidebar}
					/>
				)}
				<ProjectsSidebar
					projects={projects}
					isCollapsed={isSidebarCollapsed}
					onToggle={handleToggleSidebar}
					onProjectClick={handleProjectClick}
					onCreateProject={handleOpenCreateDialog}
					canManageProjects={canManageProjects}
				/>
			</div>

			{/* Main content */}
			<main className='flex-1 min-w-0 bg-layout-100 overflow-auto'>
				<div className='p-6 sm:p-8'>
					{/* Mobile header with hamburger */}
					<div className='lg:hidden mb-6'>
						<div className='flex items-center justify-between p-4 bg-surface-50 rounded-xl shadow-sm'>
							<button
								onClick={handleToggleSidebar}
								className='p-2 rounded-lg hover:bg-surface-50 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-surface-50 transition-colors duration-200'
								aria-label='Toggle sidebar'>
								<svg
									className='w-6 h-6 text-muted'
									fill='none'
									stroke='currentColor'
									viewBox='0 0 24 24'>
									<path
										strokeLinecap='round'
										strokeLinejoin='round'
										strokeWidth={2}
										d='M4 6h16M4 12h16M4 18h16'
									/>
								</svg>
							</button>
							<Typography
								variant='h4'
								weight='semibold'
								color='primary'>
								Projects
							</Typography>
							<div className='w-10' /> {/* Spacer for centering */}
						</div>
					</div>

					{/* Desktop header */}
					<div className='hidden lg:block'>
						<ProjectsHeader
							onCreateProject={handleOpenCreateDialog}
							canManageProjects={canManageProjects}
						/>
					</div>

					{/* Projects grid */}
					<ProjectsGrid
						projects={projects}
						isLoading={isLoading}
						canManageProjects={canManageProjects}
						onEditProject={handleEditProject}
						onDeleteProject={handleDeleteProject}
					/>
				</div>
			</main>

			{/* Project Form Dialog (Create/Edit) */}
			{isProjectDialogOpen && (
				<ProjectFormDialog
					isOpen={isProjectDialogOpen}
					onClose={handleCloseProjectDialog}
					mode={projectDialogMode}
					project={selectedProject}
					onProjectCreated={handleProjectCreated}
					onProjectUpdated={handleProjectUpdated}
				/>
			)}

			{/* Delete Project Dialog */}
			{isDeleteDialogOpen && (
				<DeleteProjectDialog
					isOpen={isDeleteDialogOpen}
					onClose={handleCloseDeleteDialog}
					project={selectedProject}
					onProjectDeleted={handleProjectDeleted}
				/>
			)}
		</div>
	);
};

ProjectsPage.getLayout = (page) => {
	return <AppTemplate>{page}</AppTemplate>;
};

// Protect this page with authentication
export default withAuth(ProjectsPage);
