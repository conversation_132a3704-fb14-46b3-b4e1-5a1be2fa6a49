import { NextPageWithLayout } from './_app';
import { AuthTemplate } from '@/components/templates';
import { Typography, Button } from '@/components/atoms';
import { ShieldExclamationIcon } from '@heroicons/react/24/outline';
import { useRouter } from 'next/router';
import { useAuthStore } from '@/providers/auth-store-provider';

const UnauthorizedPage: NextPageWithLayout = () => {
	const router = useRouter();
	const logout = useAuthStore((state) => state.logout);

	const handleGoBack = () => {
		router.back();
	};

	const handleGoHome = () => {
		router.push('/');
	};

	const handleLogout = async () => {
		await logout();
		router.push('/signin');
	};

	return (
		<div className='min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8'>
			<div className='max-w-md w-full text-center space-y-8'>
				<div>
					<ShieldExclamationIcon className='w-16 h-16 text-warning mx-auto mb-6' />
					<Typography
						variant='h2'
						className='mb-4'>
						Access Denied
					</Typography>
					<Typography
						variant='body'
						color='secondary'
						className='mb-6'>
						You don&apos;t have permission to access this page. This could be
						due to insufficient privileges or your account status.
					</Typography>
				</div>

				<div className='space-y-4'>
					<div className='flex flex-col sm:flex-row gap-3'>
						<Button
							variant='outline'
							size='md'
							onClick={handleGoBack}
							className='flex-1'>
							Go Back
						</Button>
						<Button
							variant='primary'
							size='md'
							onClick={handleGoHome}
							className='flex-1'>
							Go Home
						</Button>
					</div>
					<Button
						variant='ghost'
						size='sm'
						onClick={handleLogout}
						className='w-full'>
						Sign Out
					</Button>
				</div>

				<div className='text-center'>
					<Typography
						variant='caption'
						color='tertiary'>
						If you believe this is an error, please contact support.
					</Typography>
				</div>
			</div>
		</div>
	);
};

UnauthorizedPage.getLayout = (page) => {
	return <AuthTemplate>{page}</AuthTemplate>;
};

export default UnauthorizedPage;
