import React from 'react';
import { AppTemplate } from '@/components/templates';
import { NextPageWithLayout } from '../_app';
import { Typography } from '@/components/atoms';
import { GenerationsList } from '@/components/organisms';

const GenerationsIndexPage: NextPageWithLayout = () => {
	return (
		<div className='p-4 sm:p-6 lg:p-8 max-w-7xl mx-auto'>
			{/* Header */}
			<div className='mb-6 sm:mb-8'>
				<Typography
					variant='h1'
					weight='bold'
					className='mb-2 text-2xl sm:text-3xl lg:text-4xl'>
					Generations
				</Typography>
				<Typography
					variant='body'
					color='secondary'
					className='text-sm sm:text-base'>
					Manage and view all your AI-generated content
				</Typography>
			</div>

			{/* Generations List */}
			<GenerationsList showCreateButton />
		</div>
	);
};

GenerationsIndexPage.getLayout = (page) => {
	return <AppTemplate>{page}</AppTemplate>;
};

export default GenerationsIndexPage;
