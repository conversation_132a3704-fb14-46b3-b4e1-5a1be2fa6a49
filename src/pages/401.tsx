import { Typo<PERSON>, <PERSON><PERSON>, <PERSON> } from '@/components/atoms';
import { AuthTemplate } from '@/components/templates';
import Link from 'next/link';

export default function UnauthorizedPage() {
	return (
		<AuthTemplate>
			<Card className='max-w-md mx-auto text-center'>
				<div className='space-y-6'>
					<div>
						<Typography
							variant='display-sm'
							weight='bold'
							color='negative'
							className='mb-2'>
							401
						</Typography>
						<Typography
							variant='h2'
							className='mb-4'>
							Unauthorized
						</Typography>
						<Typography
							variant='body-lg'
							color='secondary'>
							You need to sign in to access this page.
						</Typography>
					</div>
					<div className='space-y-3'>
						<Link href='/signin'>
							<Button
								variant='primary'
								size='lg'
								className='w-full'>
								Sign In
							</Button>
						</Link>
						<Link href='/'>
							<Button
								variant='outline'
								size='lg'
								className='w-full'>
								Go Home
							</Button>
						</Link>
					</div>
				</div>
			</Card>
		</AuthTemplate>
	);
}
