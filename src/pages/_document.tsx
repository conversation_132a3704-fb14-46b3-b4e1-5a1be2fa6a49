import { Html, Head, Main, NextScript } from 'next/document';

export default function Document() {
	return (
		<Html lang='en'>
			<Head>
				{/* Optimize resource loading for WebContainer */}
				<link
					rel='preconnect'
					href='https://w-corp-staticblitz.com'
				/>
				<link
					rel='dns-prefetch'
					href='https://w-corp-staticblitz.com'
				/>
			</Head>
			<body className='antialiased min-h-svh'>
				<Main />
				<NextScript />
			</body>
		</Html>
	);
}
