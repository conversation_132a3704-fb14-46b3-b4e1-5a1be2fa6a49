import { Typo<PERSON>, <PERSON><PERSON>, <PERSON> } from '@/components/atoms';
import { AuthTemplate } from '@/components/templates';
import Link from 'next/link';

export default function NotFoundPage() {
	return (
		<AuthTemplate>
			<Card className='max-w-md mx-auto text-center'>
				<div className='space-y-6'>
					<div>
						<Typography
							variant='display-sm'
							weight='bold'
							color='primary'
							className='mb-2'>
							404
						</Typography>
						<Typography
							variant='h2'
							className='mb-4'>
							Page Not Found
						</Typography>
						<Typography
							variant='body-lg'
							color='secondary'>
							The page you&apos;re looking for doesn&apos;t exist or has been
							moved.
						</Typography>
					</div>
					<Link href='/'>
						<Button
							variant='primary'
							size='lg'>
							Go Home
						</Button>
					</Link>
				</div>
			</Card>
		</AuthTemplate>
	);
}
