import { Typo<PERSON>, <PERSON><PERSON>, <PERSON> } from '@/components/atoms';
import { AuthTemplate } from '@/components/templates';
import Link from 'next/link';

export default function ForbiddenPage() {
	return (
		<AuthTemplate>
			<Card className='max-w-md mx-auto text-center'>
				<div className='space-y-6'>
					<div>
						<Typography
							variant='display-sm'
							weight='bold'
							color='negative'
							className='mb-2'>
							403
						</Typography>
						<Typography
							variant='h2'
							className='mb-4'>
							Access Forbidden
						</Typography>
						<Typography
							variant='body-lg'
							color='secondary'>
							You don&apos;t have permission to access this resource.
						</Typography>
					</div>
					<Link href='/'>
						<Button
							variant='primary'
							size='lg'>
							Go Home
						</Button>
					</Link>
				</div>
			</Card>
		</AuthTemplate>
	);
}
