import type { NextPageWithLayout } from '../_app';
import Link from 'next/link';
import { RegisterForm, AuthTemplate } from '@/components/templates';
import { Typography } from '@/components/atoms';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import InvitationApi from '@/api/InvitationApi';
import {
	isApiSuccess,
	extractInvitationData,
	getApiErrorMessage,
} from '@/common/utils/apiResponse';

const RegisterPage: NextPageWithLayout = () => {
	const router = useRouter();
	const [invitation, setInvitation] = useState<
		| {
				token?: string;
				email?: string;
				status?: string;
		  }
		| undefined
	>(undefined);
	const [invitationError, setInvitationError] = useState<string | undefined>(
		undefined,
	);
	const [isLoadingInvitation, setIsLoadingInvitation] = useState(false);

	useEffect(() => {
		const invitationToken = router.query.invitation as string;

		if (router.isReady && invitationToken) {
			setIsLoadingInvitation(true);

			const fetchInvitation = async () => {
				try {
					const invitationApi = new InvitationApi();
					const response = await invitationApi.getInvitation(invitationToken);

					if (isApiSuccess(response)) {
						const invitationData = extractInvitationData(response);
						if (invitationData) {
							setInvitation({
								token: invitationToken,
								email: invitationData.email,
								status: invitationData.status,
							});
							setInvitationError(undefined);
						} else {
							setInvitationError('Invalid invitation data received');
							setInvitation(undefined);
						}
					} else {
						const errorMessage = getApiErrorMessage(response);
						setInvitationError(errorMessage);
						setInvitation(undefined);
					}
				} catch (error) {
					console.error('Error fetching invitation:', error);
					setInvitationError('Failed to validate invitation token');
					setInvitation(undefined);
				} finally {
					setIsLoadingInvitation(false);
				}
			};

			fetchInvitation();
		} else if (router.isReady) {
			setIsLoadingInvitation(false);
		}
	}, [router.isReady, router.query.invitation]);

	return (
		<div className='w-full max-w-sm sm:max-w-md mx-auto px-2 sm:px-3 py-4 sm:py-6'>
			<RegisterForm
				invitation={invitation}
				invitationError={invitationError}
				isLoadingInvitation={isLoadingInvitation}
			/>

			<div className='mt-3 text-center'>
				<Typography
					variant='body-sm'
					color='secondary'>
					Do you have an account?{' '}
					<Link
						href='signin'
						className='font-semibold text-accent-content-default hover:underline focus:outline-none focus:ring-2 focus:ring-accent-border-focus rounded'>
						Login
					</Link>
				</Typography>
			</div>
		</div>
	);
};

RegisterPage.getLayout = (page) => {
	return <AuthTemplate>{page}</AuthTemplate>;
};

export default RegisterPage;
