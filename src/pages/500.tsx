import { Typo<PERSON>, <PERSON><PERSON>, <PERSON> } from '@/components/atoms';
import { AuthTemplate } from '@/components/templates';
import Link from 'next/link';

export default function ServerErrorPage() {
	return (
		<AuthTemplate>
			<Card className='max-w-md mx-auto text-center'>
				<div className='space-y-6'>
					<div>
						<Typography
							variant='display-sm'
							weight='bold'
							color='negative'
							className='mb-2'>
							500
						</Typography>
						<Typography
							variant='h2'
							className='mb-4'>
							Server Error
						</Typography>
						<Typography
							variant='body-lg'
							color='secondary'>
							Something went wrong on our end. Please try again later.
						</Typography>
					</div>
					<div className='space-y-3'>
						<Button
							variant='primary'
							size='lg'
							onClick={() => window.location.reload()}
							className='w-full'>
							Try Again
						</Button>
						<Link href='/'>
							<Button
								variant='outline'
								size='lg'
								className='w-full'>
								Go Home
							</Button>
						</Link>
					</div>
				</div>
			</Card>
		</AuthTemplate>
	);
}
